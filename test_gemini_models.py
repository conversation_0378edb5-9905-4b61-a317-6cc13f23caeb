"""
Test available Gemini models and basic functionality.
"""

import google.generativeai as genai
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_gemini_models():
    """Test available Gemini models."""
    print("🔍 Testing Gemini API and Available Models")
    print("=" * 50)
    
    try:
        # Configure API
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("❌ No GEMINI_API_KEY found in environment")
            return False
        
        genai.configure(api_key=api_key)
        print(f"✅ API configured with key: {api_key[:10]}...")
        
        # List available models
        print("\n📋 Available models:")
        models = genai.list_models()
        for model in models:
            if 'generateContent' in model.supported_generation_methods:
                print(f"  ✅ {model.name} - {model.display_name}")
        
        # Test different model names
        model_names_to_test = [
            'gemini-1.5-flash',
            'gemini-1.5-pro',
            'gemini-pro',
            'models/gemini-1.5-flash',
            'models/gemini-1.5-pro',
            'models/gemini-pro'
        ]
        
        for model_name in model_names_to_test:
            print(f"\n🧪 Testing model: {model_name}")
            try:
                model = genai.GenerativeModel(model_name)
                response = model.generate_content("Hello! How are you?")
                print(f"  ✅ Success: {response.text[:100]}...")
                
                # Test emotion analysis
                emotion_prompt = """
                Analyze the emotional content of this text: "I'm so excited about my new job!"
                Return a JSON response with:
                - emotion: detected emotion
                - confidence: confidence level (0-1)
                - reasoning: brief explanation
                """
                
                emotion_response = model.generate_content(emotion_prompt)
                print(f"  ✅ Emotion analysis: {emotion_response.text[:100]}...")
                
                print(f"  🎉 Model {model_name} works perfectly!")
                return model_name
                
            except Exception as e:
                print(f"  ❌ Failed: {str(e)}")
        
        print("\n❌ No working models found")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    working_model = test_gemini_models()
    if working_model:
        print(f"\n🚀 Use this model name: {working_model}")
    else:
        print("\n⚠️  Please check your API key and try again.")
