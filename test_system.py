"""
Test script for the AI Companion System.
Verifies that all components work correctly.
"""

import os
import sys
import asyncio
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from monitoring_service import monitoring

def test_imports():
    """Test that all modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        from config import settings
        print("✅ Config module imported")
        
        from models import MemoryType, EmotionType, InteractionType
        print("✅ Models module imported")
        
        from memory_service import MemoryService
        print("✅ Memory service imported")
        
        from gemini_service import GeminiService
        print("✅ Gemini service imported")
        
        from learning_service import LearningService
        print("✅ Learning service imported")
        
        from conversation_service import ConversationService
        print("✅ Conversation service imported")

        from emotional_intelligence_service import EmotionalIntelligenceService
        print("✅ Emotional intelligence service imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_memory_service():
    """Test memory service functionality with monitoring."""
    print("\n🧠 Testing memory service...")
    start_time = time.time()

    try:
        from memory_service import MemoryService
        from models import MemoryType, InteractionType, EmotionType

        memory_service = MemoryService()

        # Test personal memory storage
        personal_memory = memory_service.store_personal_memory(
            user_id="test_user",
            content="I love reading science fiction books",
            interaction_type=InteractionType.INTEREST,
            emotion=EmotionType.JOY
        )
        print(f"✅ Personal memory stored: {personal_memory.id}")
        monitoring.record_metric('test_memory_storage', 1.0, {'type': 'personal'})

        # Test universal memory storage
        universal_memory = memory_service.store_universal_memory(
            content="Science fiction is a popular genre among tech enthusiasts",
            interaction_type=InteractionType.CONVERSATION,
            topic_tags=["books", "science fiction", "technology"]
        )
        print(f"✅ Universal memory stored: {universal_memory.id}")
        monitoring.record_metric('test_memory_storage', 1.0, {'type': 'universal'})

        # Test memory retrieval
        relevant_memories = memory_service.retrieve_relevant_memories(
            user_id="test_user",
            query="books",
            memory_type=MemoryType.PERSONAL,
            limit=5
        )
        print(f"✅ Retrieved {len(relevant_memories)} relevant memories")
        monitoring.record_metric('test_memory_retrieval', len(relevant_memories))

        # Test user profile
        user_profile = memory_service.get_user_profile("test_user")
        if user_profile:
            print(f"✅ User profile found: {user_profile.user_id}")
        else:
            print("⚠️  User profile not found (expected for new user)")

        # Test storage service health
        storage_health = monitoring.check_health('storage_test', lambda: True)
        print(f"✅ Storage health: {storage_health.status.value}")

        duration = time.time() - start_time
        monitoring.record_request_time(duration, 'memory_service_test')

        return True

    except Exception as e:
        print(f"❌ Memory service error: {e}")
        monitoring.record_error(e, 'memory_service_test')
        return False

def test_gemini_service():
    """Test Gemini service functionality."""
    print("\n🤖 Testing Gemini service...")
    
    try:
        from gemini_service import GeminiService
        
        gemini_service = GeminiService()
        
        # Test emotion analysis
        emotion_result = gemini_service.analyze_emotion("I'm feeling really happy today!")
        print(f"✅ Emotion analysis: {emotion_result['emotion']} (confidence: {emotion_result['confidence']:.2f})")
        
        # Test context analysis
        conversation_history = [
            {'role': 'user', 'content': 'I love reading science fiction'},
            {'role': 'assistant', 'content': 'That sounds interesting! What books do you enjoy?'}
        ]
        
        context_result = gemini_service.analyze_context(conversation_history)
        print(f"✅ Context analysis: {len(context_result.get('topics', []))} topics identified")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini service error: {e}")
        print("⚠️  Make sure GEMINI_API_KEY is set in your .env file")
        return False

def test_conversation_service():
    """Test conversation service functionality."""
    print("\n💬 Testing conversation service...")
    
    try:
        from conversation_service import ConversationService
        
        conversation_service = ConversationService()
        
        # Start conversation
        conversation_id = conversation_service.start_conversation("test_user", "Test User")
        print(f"✅ Conversation started: {conversation_id}")
        
        # Process message
        result = conversation_service.process_message(
            conversation_id=conversation_id,
            user_message="Hello! I'm feeling great today.",
            user_id="test_user"
        )
        
        if 'error' in result:
            print(f"⚠️  Conversation error: {result['error']}")
        else:
            print(f"✅ Response generated: {result['response'][:100]}...")
            print(f"✅ Emotion detected: {result['emotion_analysis']['emotion']}")
        
        # Get conversation history
        history = conversation_service.get_conversation_history(conversation_id)
        print(f"✅ Conversation history: {len(history)} messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Conversation service error: {e}")
        return False

def test_learning_service():
    """Test learning service functionality."""
    print("\n🎓 Testing learning service...")
    
    try:
        from learning_service import LearningService
        from memory_service import MemoryService
        from gemini_service import GeminiService
        
        memory_service = MemoryService()
        gemini_service = GeminiService()
        learning_service = LearningService(memory_service, gemini_service)
        
        # Test user preference analysis
        conversation_history = [
            {'role': 'user', 'content': 'I love reading science fiction books'},
            {'role': 'user', 'content': 'My favorite author is Isaac Asimov'},
            {'role': 'user', 'content': 'I enjoy discussing technology and AI'}
        ]
        
        preferences = learning_service.analyze_user_preferences("test_user", conversation_history)
        print(f"✅ User preferences analyzed: {len(preferences.get('topic_interests', []))} interests found")
        
        # Test content curation
        content = learning_service.curate_personalized_content("test_user", "conversation_starters")
        print(f"✅ Personalized content curated: {len(content)} items")
        
        return True

    except Exception as e:
        print(f"❌ Learning service error: {e}")
        monitoring.record_error(e, 'learning_service_test')
        return False

def test_emotional_intelligence():
    """Test emotional intelligence service functionality."""
    print("\n💭 Testing emotional intelligence service...")
    start_time = time.time()

    try:
        from emotional_intelligence_service import EmotionalIntelligenceService
        from gemini_service import GeminiService
        from models import EmotionType, EmotionalState

        gemini_service = GeminiService()
        ei_service = EmotionalIntelligenceService(gemini_service)

        # Test emotion analysis
        test_messages = [
            "I'm feeling really happy today! Got a promotion at work!",
            "I'm so stressed about this upcoming exam...",
            "Just feeling neutral, nothing special happening.",
            "I'm really sad because my pet passed away yesterday."
        ]

        for i, message in enumerate(test_messages):
            emotional_state = ei_service.analyze_advanced_emotion(
                text=message,
                user_id=f"test_user_{i}"
            )
            print(f"✅ Analyzed emotion for message {i+1}: {emotional_state.primary_emotion.value}")
            monitoring.record_metric('test_emotion_analysis', 1.0, {'message_id': i})

        # Test empathy model creation
        test_emotional_state = EmotionalState(
            primary_emotion=EmotionType.SADNESS,
            intensity=0.8,
            valence=-0.6,
            confidence=0.9
        )

        empathy_model = ei_service.create_empathy_model(
            user_id="test_user_empathy",
            emotional_state=test_emotional_state,
            conversation_history=[
                {"role": "user", "content": "I'm feeling really down today"},
                {"role": "assistant", "content": "I'm sorry to hear that. Would you like to talk about it?"}
            ]
        )
        print(f"✅ Created empathy model with strategy: {empathy_model.response_strategy}")

        # Test emotional insights
        insights = ei_service.get_emotional_insights("test_user_0")
        print(f"✅ Generated emotional insights: {insights.get('total_interactions', 0)} interactions")

        duration = time.time() - start_time
        monitoring.record_request_time(duration, 'emotional_intelligence_test')

        return True

    except Exception as e:
        print(f"❌ Emotional intelligence test failed: {e}")
        return False

def test_storage_service():
    """Test persistent storage service functionality."""
    print("\n💾 Testing storage service...")
    start_time = time.time()

    try:
        from storage_service import PersistentStorageService
        from models import PersonalMemory, UserProfile, MemoryType, InteractionType, EmotionType

        storage_service = PersistentStorageService()

        # Test user profile storage and retrieval
        test_profile = UserProfile(
            user_id="test_storage_user",
            name="Test User",
            interests=["AI", "technology", "books"]
        )

        # Store profile
        success = storage_service.store_user_profile(test_profile)
        print(f"✅ Profile storage: {success}")
        monitoring.record_metric('test_profile_storage', 1.0 if success else 0.0)

        # Retrieve profile
        retrieved_profile = storage_service.retrieve_user_profile("test_storage_user")
        if retrieved_profile and retrieved_profile.user_id == "test_storage_user":
            print(f"✅ Profile retrieval: {retrieved_profile.name}")
            monitoring.record_metric('test_profile_retrieval', 1.0)
        else:
            print("❌ Profile retrieval failed")
            monitoring.record_metric('test_profile_retrieval', 0.0)

        # Test memory storage
        test_memory = PersonalMemory(
            id="test_memory_001",
            user_id="test_storage_user",
            memory_type=MemoryType.PERSONAL,
            interaction_type=InteractionType.CONVERSATION,
            content="Test memory for storage validation",
            emotion=EmotionType.JOY
        )

        # Store memory
        memory_success = storage_service.store_memory(test_memory)
        print(f"✅ Memory storage: {memory_success}")
        monitoring.record_metric('test_memory_persistence', 1.0 if memory_success else 0.0)

        # Test conversation storage
        test_messages = [
            {"role": "user", "content": "Hello", "timestamp": "2024-01-01T00:00:00Z"},
            {"role": "assistant", "content": "Hi there!", "timestamp": "2024-01-01T00:00:01Z"}
        ]

        conv_success = storage_service.store_conversation(
            conversation_id="test_conv_001",
            user_id="test_storage_user",
            messages=test_messages,
            summary="Test conversation",
            topics=["greeting"]
        )
        print(f"✅ Conversation storage: {conv_success}")
        monitoring.record_metric('test_conversation_storage', 1.0 if conv_success else 0.0)

        # Retrieve conversation
        retrieved_conv = storage_service.retrieve_conversation("test_conv_001")
        if retrieved_conv and len(retrieved_conv['messages']) == 2:
            print(f"✅ Conversation retrieval: {len(retrieved_conv['messages'])} messages")
            monitoring.record_metric('test_conversation_retrieval', 1.0)
        else:
            print("❌ Conversation retrieval failed")
            monitoring.record_metric('test_conversation_retrieval', 0.0)

        # Test database health
        db_health = storage_service.redis_available
        print(f"✅ Database health: {'Redis available' if db_health else 'SQLite only'}")
        monitoring.record_metric('test_database_health', 1.0 if db_health else 0.5)

        duration = time.time() - start_time
        monitoring.record_request_time(duration, 'storage_service_test')

        return True

    except Exception as e:
        print(f"❌ Storage service error: {e}")
        monitoring.record_error(e, 'storage_service_test')
        return False

def test_monitoring_service():
    """Test monitoring and alerting functionality."""
    print("\n📊 Testing monitoring service...")
    start_time = time.time()

    try:
        # Test metric recording
        monitoring.record_metric('test_metric', 42.0, {'test': 'true'})
        print("✅ Metric recording")

        # Test alert creation
        monitoring.create_alert(
            monitoring.AlertLevel.INFO,
            "Test alert for system validation",
            'test_component'
        )
        print("✅ Alert creation")

        # Test health check
        def dummy_health_check():
            return True

        health_result = monitoring.check_health('test_component', dummy_health_check)
        print(f"✅ Health check: {health_result.status.value}")

        # Test rate limiting
        rate_limit_ok = monitoring.check_rate_limit('test_user')
        print(f"✅ Rate limiting: {'OK' if rate_limit_ok else 'Limited'}")

        # Test system health overview
        system_health = monitoring.get_system_health()
        print(f"✅ System health: {system_health['overall_status']}")

        duration = time.time() - start_time
        monitoring.record_request_time(duration, 'monitoring_service_test')

        return True

    except Exception as e:
        print(f"❌ Monitoring service error: {e}")
        monitoring.record_error(e, 'monitoring_service_test')
        return False

def main():
    """Run all tests."""
    print("🧪 AI Companion System Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Storage Service Test", test_storage_service),
        ("Memory Service Test", test_memory_service),
        ("Gemini Service Test", test_gemini_service),
        ("Emotional Intelligence Test", test_emotional_intelligence),
        ("Learning Service Test", test_learning_service),
        ("Conversation Service Test", test_conversation_service),
        ("Monitoring Service Test", test_monitoring_service)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    # Generate test report
    print(f"\n{'='*50}")
    print("📊 DETAILED TEST REPORT")
    print(f"{'='*50}")

    # Show system health
    system_health = monitoring.get_system_health()
    print(f"System Status: {system_health['overall_status'].upper()}")
    print(f"Average Response Time: {system_health['metrics']['average_response_time']:.3f}s")
    print(f"Error Rate: {system_health['metrics']['error_rate']:.2%}")

    if passed == total:
        print("\n🎉 All tests passed! The AI Companion System is ready for production.")
        print("\nTo start the system, run:")
        print("python main.py")

        monitoring.create_alert(
            monitoring.AlertLevel.INFO,
            "All system tests passed successfully",
            'test_suite'
        )
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\nMake sure to:")
        print("1. Install all dependencies: pip install -r requirements.txt")
        print("2. Set your GEMINI_API_KEY in the .env file")
        print("3. Have Redis running (optional, for full functionality)")
        print("4. Check database connectivity")

        monitoring.create_alert(
            monitoring.AlertLevel.WARNING,
            f"Test suite failed: {total - passed} out of {total} tests failed",
            'test_suite'
        )

if __name__ == "__main__":
    main() 