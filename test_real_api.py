"""
Test the AI Companion System with real Gemini API.
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

def test_real_gemini_api():
    """Test the system with real Gemini API."""
    print("🤖 Testing AI Companion System with Real Gemini API")
    print("=" * 60)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from models import EmotionType, EmotionalState, EmpathyModel
        from emotional_intelligence_service import EmotionalIntelligenceService
        from gemini_service import GeminiService
        from conversation_service import ConversationService
        print("✅ All modules imported successfully")
        
        # Initialize services
        print("\n🔧 Initializing services...")
        conversation_service = ConversationService()
        print("✅ Conversation service initialized")
        
        # Test emotion analysis with real API
        print("\n🎭 Testing real emotion analysis...")
        test_messages = [
            "I'm so excited! I just got my dream job and I can't believe it's happening!",
            "I'm feeling really anxious about my upcoming presentation tomorrow...",
            "I'm devastated. My grandmother passed away last night and I don't know how to cope.",
            "Just had a normal day at work, nothing special really happened.",
            "I'm frustrated with my computer - it keeps crashing when I'm trying to work!"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n--- Test Message {i} ---")
            print(f"User: {message}")
            
            # Analyze emotion
            emotional_state = conversation_service.emotional_intelligence.analyze_advanced_emotion(
                text=message,
                user_id=f"test_user_{i}"
            )
            
            print(f"🎭 Detected emotion: {emotional_state.primary_emotion.value}")
            print(f"💪 Intensity: {emotional_state.intensity:.2f}")
            print(f"😊 Valence: {emotional_state.valence:.2f}")
            print(f"🎯 Confidence: {emotional_state.confidence:.2f}")
            
            # Create empathy model
            empathy_model = conversation_service.emotional_intelligence.create_empathy_model(
                user_id=f"test_user_{i}",
                emotional_state=emotional_state,
                conversation_history=[{"role": "user", "content": message}]
            )
            
            print(f"🤗 Response strategy: {empathy_model.response_strategy}")
            print(f"💝 Support needed: {empathy_model.should_provide_support()}")
            
            # Generate empathetic response
            response = conversation_service.emotional_intelligence.generate_empathetic_response(
                empathy_model=empathy_model,
                conversation_history=[{"role": "user", "content": message}],
                user_message=message
            )
            
            print(f"🤖 AI Response: {response}")
            print("-" * 50)
        
        # Test conversation flow
        print("\n💬 Testing full conversation flow...")
        user_id = "test_conversation_user"
        conversation_id = conversation_service.start_conversation(user_id, "Test User")
        
        test_conversation = [
            "Hi there! I'm feeling a bit lonely today.",
            "Yeah, I've been working from home for months and I miss human interaction.",
            "That's a great idea! I should probably call some friends.",
            "Thank you so much for listening and giving me good advice!"
        ]
        
        for message in test_conversation:
            print(f"\nUser: {message}")
            
            result = conversation_service.process_message(
                conversation_id=conversation_id,
                user_message=message,
                user_id=user_id
            )
            
            print(f"AI: {result['response']}")
            
            # Show emotional insights
            if 'emotional_state' in result:
                emotion_data = result['emotional_state']
                print(f"   [Emotion: {emotion_data['primary_emotion']}, "
                      f"Valence: {emotion_data['valence']:.2f}]")
        
        # Get emotional insights
        print("\n📊 Getting emotional insights...")
        insights = conversation_service.get_emotional_insights(user_id)
        print(f"Total interactions: {insights.get('total_interactions', 0)}")
        print(f"Average valence: {insights.get('average_valence', 0):.2f}")
        print(f"Emotional stability: {insights.get('emotional_stability', 0):.2f}")
        print(f"Recent trend: {insights.get('recent_trend', 'unknown')}")
        
        # Get empathy recommendations
        empathy_rec = conversation_service.get_empathy_recommendations(user_id)
        if 'message' not in empathy_rec:
            print(f"Support needed: {empathy_rec.get('support_needed', False)}")
            print(f"Response strategy: {empathy_rec.get('response_strategy', 'unknown')}")
        
        print("\n🎉 All tests completed successfully!")
        print("✅ The AI Companion System is working with real Gemini API!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_gemini_api()
    if success:
        print("\n🚀 System is ready for deployment!")
    else:
        print("\n⚠️  Please check the errors and try again.")
        sys.exit(1)
