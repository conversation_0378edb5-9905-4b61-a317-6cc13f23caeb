"""
Main entry point for the AI Companion System.
Launches the Gradio web interface and manages system initialization.
"""

import os
import sys
import logging
import signal
import asyncio
from pathlib import Path
from contextlib import asynccontextmanager

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from gradio_interface import AICompanionInterface
from config import settings
from monitoring_service import monitoring, monitor_performance, AlertLevel
from storage_service import PersistentStorageService

@monitor_performance
def setup_logging():
    """Setup production-grade logging configuration."""
    # Logging is now handled by MonitoringService
    logger = logging.getLogger(__name__)
    logger.info("Logging system initialized")
    return logger

@monitor_performance
def check_dependencies():
    """Check if all required dependencies are available with monitoring."""
    print("🔍 Checking dependencies...")

    required_packages = [
        'torch',
        'transformers',
        'sentence_transformers',
        'google.generativeai',
        'gradio',
        'redis',
        'numpy',
        'scikit-learn',
        'sqlalchemy',
        'pydantic'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('.', '_'))
            print(f"✅ {package}")
            monitoring.record_metric('dependency_check', 1.0, {'package': package, 'status': 'ok'})
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
            monitoring.record_metric('dependency_check', 0.0, {'package': package, 'status': 'missing'})

    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nPlease install missing packages with:")
        print("pip install -r requirements.txt")

        monitoring.create_alert(
            AlertLevel.CRITICAL,
            f"Missing dependencies: {', '.join(missing_packages)}",
            'dependencies'
        )
        return False

    print("✅ All dependencies are installed")
    return True

def check_environment():
    """Check environment configuration."""
    print("🔍 Checking environment configuration...")
    
    # Check API keys
    if not settings.gemini_api_key:
        print("❌ GEMINI_API_KEY is not set!")
        print("Please set your Gemini API key in the .env file")
        return False
    
    print("✅ Gemini API key configured")
    
    # Check Redis connection
    try:
        import redis
        redis_client = redis.from_url(settings.redis_url)
        redis_client.ping()
        print("✅ Redis connection successful")
    except Exception as e:
        print(f"⚠️  Redis connection failed: {e}")
        print("The system will work with limited functionality")
    
    return True

def print_startup_banner():
    """Print the startup banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🤖 AI Companion System                    ║
    ║                                                              ║
    ║  A sophisticated conversational AI with dual-memory         ║
    ║  architecture and emotional intelligence                     ║
    ║                                                              ║
    ║  Features:                                                   ║
    ║  • Personal & Universal Memory Management                   ║
    ║  • Emotional Intelligence & Context Awareness               ║
    ║  • Adaptive Learning & Personalized Responses               ║
    ║  • Privacy-First Design                                     ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

@monitor_performance
def initialize_services():
    """Initialize all system services with health checks."""
    logger = logging.getLogger(__name__)

    try:
        # Initialize storage service
        print("🔧 Initializing storage service...")
        storage_service = PersistentStorageService()

        # Health check for storage
        def storage_health_check():
            try:
                if storage_service.redis_available:
                    storage_service.redis_client.ping()
                # Test database connection
                with storage_service.get_db_session() as db:
                    db.execute("SELECT 1")
                return True
            except Exception:
                return False

        health_check = monitoring.check_health('storage', storage_health_check)
        if health_check.status != monitoring.HealthStatus.HEALTHY:
            raise Exception(f"Storage service unhealthy: {health_check.message}")

        print("✅ Storage service initialized")

        # Initialize AI Companion interface
        print("🔧 Creating AI Companion interface...")
        interface = AICompanionInterface()
        print("✅ AI Companion interface created")

        return interface

    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        monitoring.record_error(e, 'initialization')
        raise

def setup_signal_handlers():
    """Setup graceful shutdown signal handlers."""
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        monitoring.create_alert(
            AlertLevel.INFO,
            f"Shutdown signal received: {signum}",
            'system'
        )
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def main():
    """Main function to run the AI Companion System with production features."""
    print_startup_banner()

    # Setup logging and monitoring
    logger = setup_logging()

    # Setup signal handlers for graceful shutdown
    setup_signal_handlers()

    print("🚀 Initializing AI Companion System...")
    monitoring.create_alert(
        AlertLevel.INFO,
        "AI Companion System starting up",
        'system'
    )

    try:
        # Check dependencies
        if not check_dependencies():
            logger.error("Missing required dependencies")
            sys.exit(1)

        # Check environment
        if not check_environment():
            logger.error("Environment configuration issues")
            sys.exit(1)

        # Initialize services
        interface = initialize_services()

        # Start health monitoring
        print("🔍 Starting health monitoring...")

        print(f"🌐 Starting web interface on port {settings.gradio_port}...")
        print(f"📱 Access the interface at: http://localhost:{settings.gradio_port}")
        print(f"📊 Health status: http://localhost:{settings.gradio_port}/health")
        print("🔄 Press Ctrl+C to stop the server")
        print("\n" + "="*60)

        # Record successful startup
        monitoring.record_metric('startup_success', 1.0)
        monitoring.create_alert(
            AlertLevel.INFO,
            "AI Companion System started successfully",
            'system'
        )

        # Launch the interface
        interface.launch(
            server_name="0.0.0.0",
            server_port=settings.gradio_port,
            share=False,
            debug=settings.debug_mode,
            show_error=True
        )

    except KeyboardInterrupt:
        print("\n🛑 Shutting down AI Companion System...")
        logger.info("System shutdown requested by user")
        monitoring.create_alert(
            AlertLevel.INFO,
            "System shutdown completed",
            'system'
        )
    except Exception as e:
        logger.error(f"Error starting AI Companion System: {e}")
        print(f"❌ Error: {e}")
        monitoring.record_error(e, 'main')
        monitoring.record_metric('startup_failure', 1.0)
        sys.exit(1)

if __name__ == "__main__":
    main() 