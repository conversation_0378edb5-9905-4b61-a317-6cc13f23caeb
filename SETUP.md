# AI Companion System - Setup Guide

This guide will help you set up and run the AI Companion System, a sophisticated conversational AI with dual-memory architecture and emotional intelligence.

## 🚀 Quick Start

### 1. Prerequisites

- **Python 3.8+** (recommended: Python 3.9 or 3.10)
- **Git** (for cloning the repository)
- **Redis** (optional, for full functionality)
- **Google Gemini API Key** (required)

### 2. Installation

```bash
# Clone or download the project
cd mandy

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
```

### 3. Configuration

Edit the `.env` file with your API keys:

```bash
# Required: Google Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: OpenAI API Key (for fallback)
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Redis URL (for full memory functionality)
REDIS_URL=redis://localhost:6379
```

### 4. Get Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the API key and add it to your `.env` file

### 5. Run the System

```bash
# Test the system first
python test_system.py

# Start the AI Companion
python main.py
```

The web interface will be available at: `http://localhost:7860`

## 🔧 Detailed Setup

### System Architecture

The AI Companion System consists of several microservices:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Interface │    │ Conversation    │    │   Memory        │
│   (Gradio)      │◄──►│   Service       │◄──►│   Service       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Gemini          │    │   Learning      │
                       │ Integration     │    │   Service       │
                       └─────────────────┘    └─────────────────┘
```

### Core Components

1. **Memory Service** (`memory_service.py`)
   - Dual-memory architecture (personal + universal)
   - Semantic memory retrieval using embeddings
   - User profile management

2. **Gemini Service** (`gemini_service.py`)
   - Emotion analysis and context understanding
   - Response generation with personality
   - Memory extraction from conversations

3. **Learning Service** (`learning_service.py`)
   - User preference analysis
   - Personalized content curation
   - Adaptive conversation style

4. **Conversation Service** (`conversation_service.py`)
   - Orchestrates all services
   - Manages conversation flow
   - Handles user interactions

5. **Web Interface** (`gradio_interface.py`)
   - Modern, responsive UI
   - Real-time chat interface
   - Memory exploration tools

### Configuration Options

The system can be configured through environment variables:

```bash
# Memory Configuration
MEMORY_TTL=2592000                    # Memory retention (30 days)
PERSONAL_MEMORY_SIZE=1000             # Max personal memories per user
UNIVERSAL_MEMORY_SIZE=10000           # Max universal memories

# Learning Configuration
LEARNING_RATE=0.1                     # Adaptation speed
ADAPTATION_THRESHOLD=5                # Interactions before adaptation
EMOTION_WEIGHT=0.3                    # Emotional context weight

# Conversation Configuration
MAX_CONTEXT_LENGTH=2000               # Context window size
RESPONSE_TEMPERATURE=0.7              # Response creativity
MAX_TOKENS=150                        # Response length limit

# System Configuration
LOG_LEVEL=INFO                        # Logging level
DEBUG_MODE=true                       # Debug mode
GRADIO_PORT=7860                      # Web interface port
```

## 🧪 Testing

### Run System Tests

```bash
python test_system.py
```

This will test:
- ✅ Module imports
- ✅ Memory service functionality
- ✅ Gemini API integration
- ✅ Learning service capabilities
- ✅ Conversation flow

### Manual Testing

1. **Start the system**: `python main.py`
2. **Open the interface**: `http://localhost:7860`
3. **Start a conversation**: Enter a user ID and begin chatting
4. **Test memory**: Use the memory explorer to search your memories
5. **Check insights**: View user statistics and patterns

## 🔒 Privacy & Security

### Data Isolation

- **Personal Memory**: Strictly isolated per user, never shared
- **Universal Memory**: Only non-sensitive, aggregated insights
- **No Cross-User Leakage**: Complete privacy boundaries

### Data Storage

- **Redis**: In-memory storage with TTL (time-to-live)
- **Automatic Cleanup**: Old memories are automatically removed
- **No Persistent Files**: All data stored in memory

### API Security

- **Environment Variables**: API keys stored securely
- **No Hardcoded Secrets**: All credentials externalized
- **Error Handling**: No sensitive data in error messages

## 🚀 Deployment

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export GEMINI_API_KEY="your_key_here"

# Run the system
python main.py
```

### Production Deployment

For production deployment on platforms like Render.com:

1. **Create a new Web Service**
2. **Set environment variables**:
   - `GEMINI_API_KEY`
   - `REDIS_URL` (if using Redis)
   - Other configuration variables

3. **Build Command**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Start Command**:
   ```bash
   python main.py
   ```

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 7860

CMD ["python", "main.py"]
```

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   pip install -r requirements.txt
   ```

2. **Gemini API Errors**
   - Check your API key in `.env`
   - Verify API key has proper permissions
   - Check API quota limits

3. **Redis Connection Errors**
   - Install Redis: `sudo apt-get install redis-server`
   - Start Redis: `sudo systemctl start redis`
   - Or use cloud Redis service

4. **Memory Issues**
   - Reduce `PERSONAL_MEMORY_SIZE` and `UNIVERSAL_MEMORY_SIZE`
   - Increase `MEMORY_TTL` for shorter retention
   - Monitor system memory usage

### Debug Mode

Enable debug mode for detailed logging:

```bash
export DEBUG_MODE=true
python main.py
```

### Log Files

Check the log file for detailed error information:

```bash
tail -f ai_companion.log
```

## 📚 Usage Examples

### Basic Conversation

```
User: "Hello! I'm feeling a bit stressed today."
AI: "I'm sorry to hear you're feeling stressed. I'm here to listen and support you. 
     What's been on your mind lately? Sometimes talking about what's bothering us 
     can help us feel better."
```

### Memory Recall

```
User: "Remember when I told you about my job interview?"
AI: "Yes, I remember you mentioned being nervous about that interview! 
     How did it go? I've been thinking about you and hoping it went well."
```

### Emotional Support

```
User: "I'm really sad about losing my pet."
AI: "I'm so sorry to hear about your loss. Losing a pet is incredibly difficult 
     - they become such an important part of our lives and families. 
     It's completely normal to feel this way. Would you like to tell me 
     about your pet? Sometimes sharing memories can help with the healing process."
```

## 🔮 Future Enhancements

### Planned Features

- **Voice Interface**: Speech-to-text and text-to-speech
- **Image Analysis**: Understanding and responding to images
- **Multi-Modal Memory**: Storing and retrieving images, audio
- **Advanced Analytics**: Detailed user behavior insights
- **API Endpoints**: RESTful API for integration
- **Mobile App**: Native mobile application

### Research Integration

- **Neural-Symbolic Reasoning**: Advanced reasoning capabilities
- **Neuro-Inspired Computing**: Brain-inspired memory models
- **Advanced NLP**: State-of-the-art language understanding
- **Emotional AI**: Enhanced emotional intelligence

## 🤝 Contributing

### Development Setup

1. **Fork the repository**
2. **Create a feature branch**
3. **Make your changes**
4. **Add tests** for new functionality
5. **Submit a pull request**

### Code Style

- Follow PEP 8 guidelines
- Use type hints
- Add docstrings to all functions
- Write comprehensive tests

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues:

1. **Check the troubleshooting section**
2. **Review the logs**: `tail -f ai_companion.log`
3. **Run tests**: `python test_system.py`
4. **Create an issue** with detailed error information

---

**Happy chatting with your AI companion! 🤖💬** 