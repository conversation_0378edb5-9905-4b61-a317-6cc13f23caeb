"""
Memory Service for the AI Companion System.
Implements dual-memory architecture with personal and universal memory management.
"""

import redis
import json
import hashlib
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Any, Tuple
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

from models import (
    MemoryEntry, PersonalMemory, UniversalMemory, MemoryType,
    InteractionType, EmotionType, UserProfile, ContextualMemory
)
from config import settings
from storage_service import PersistentStorageService

class MemoryService:
    """Core memory service implementing dual-memory architecture."""
    
    def __init__(self):
        """Initialize the memory service."""
        self.storage_service = PersistentStorageService()
        self.redis_client = self.storage_service.redis_client if self.storage_service.redis_available else None
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.memory_ttl = settings.memory_ttl

        # Initialize embedding cache
        self.embedding_cache = {}
        
    def _get_embedding(self, text: str) -> np.ndarray:
        """Get embedding for text with caching."""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        if text_hash not in self.embedding_cache:
            embedding = self.embedding_model.encode([text])[0]
            self.embedding_cache[text_hash] = embedding
            
        return self.embedding_cache[text_hash]
    
    def _create_memory_key(self, user_id: str, memory_type: MemoryType, memory_id: str) -> str:
        """Create Redis key for memory storage."""
        return f"memory:{memory_type.value}:{user_id}:{memory_id}"
    
    def _create_user_profile_key(self, user_id: str) -> str:
        """Create Redis key for user profile."""
        return f"profile:{user_id}"
    
    def _create_embedding_key(self, user_id: str, memory_type: MemoryType) -> str:
        """Create Redis key for embeddings."""
        return f"embeddings:{memory_type.value}:{user_id}"
    
    def store_personal_memory(
        self,
        user_id: str,
        content: str,
        interaction_type: InteractionType,
        emotion: Optional[EmotionType] = None,
        context: Optional[Dict[str, Any]] = None,
        importance: Optional[float] = None
    ) -> PersonalMemory:
        """Store a personal memory entry for a specific user."""
        
        # Create content hash for ID generation
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        memory_id = f"personal_{user_id}_{content_hash}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        
        # Calculate importance if not provided
        if importance is None:
            user_profile = self.get_user_profile(user_id)
            user_frequency = user_profile.interaction_count if user_profile else 0
            importance = calculate_memory_importance(
                emotion, interaction_type, user_frequency, len(content)
            )
        
        # Create personal memory entry
        memory = PersonalMemory(
            id=memory_id,
            user_id=user_id,
            memory_type=MemoryType.PERSONAL,
            interaction_type=interaction_type,
            content=content,
            context=context or {},
            emotion=emotion,
            importance=importance
        )
        
        # Store in persistent storage
        self.storage_service.store_memory(memory)
        
        # Store embedding for similarity search
        embedding = self._get_embedding(content)
        if self.redis_client:
            embedding_key = self._create_embedding_key(user_id, MemoryType.PERSONAL)

            # Store embedding with memory ID
            embedding_data = {
                'memory_id': memory_id,
                'embedding': embedding.tolist(),
                'importance': importance,
                'created_at': memory.created_at.isoformat()
            }

            self.redis_client.lpush(embedding_key, json.dumps(embedding_data))
            self.redis_client.expire(embedding_key, self.memory_ttl)
        
        # Update user profile
        self._update_user_profile(user_id, interaction_type, emotion)
        
        return memory
    
    def store_universal_memory(
        self,
        content: str,
        interaction_type: InteractionType,
        topic_tags: Optional[List[str]] = None,
        emotion: Optional[EmotionType] = None,
        context: Optional[Dict[str, Any]] = None,
        source_user_id: Optional[str] = None
    ) -> UniversalMemory:
        """Store a universal memory entry shared across all users."""
        
        # Create content hash for ID generation
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        memory_id = f"universal_{content_hash}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        
        # Check if similar universal memory already exists
        existing_memory = self._find_similar_universal_memory(content)
        if existing_memory:
            # Update existing memory
            existing_memory.source_count += 1
            existing_memory.last_accessed = datetime.now(timezone.utc)
            existing_memory.access_count += 1
            
            # Update in Redis
            memory_key = self._create_memory_key("", MemoryType.UNIVERSAL, existing_memory.id)
            self.redis_client.setex(
                memory_key,
                self.memory_ttl,
                existing_memory.json()
            )
            
            return existing_memory
        
        # Create new universal memory entry
        memory = UniversalMemory(
            id=memory_id,
            user_id="",  # Empty for universal memory
            memory_type=MemoryType.UNIVERSAL,
            interaction_type=interaction_type,
            content=content,
            context=context or {},
            emotion=emotion,
            topic_tags=topic_tags or [],
            source_count=1
        )
        
        # Store in Redis
        memory_key = self._create_memory_key("", MemoryType.UNIVERSAL, memory_id)
        self.redis_client.setex(
            memory_key,
            self.memory_ttl,
            memory.json()
        )
        
        # Store embedding for similarity search
        embedding = self._get_embedding(content)
        embedding_key = self._create_embedding_key("", MemoryType.UNIVERSAL)
        
        embedding_data = {
            'memory_id': memory_id,
            'embedding': embedding.tolist(),
            'global_relevance': memory.global_relevance,
            'topic_tags': topic_tags or [],
            'created_at': memory.created_at.isoformat()
        }
        
        self.redis_client.lpush(embedding_key, json.dumps(embedding_data))
        self.redis_client.expire(embedding_key, self.memory_ttl)
        
        return memory
    
    def retrieve_relevant_memories(
        self,
        user_id: str,
        query: str,
        memory_type: MemoryType = MemoryType.PERSONAL,
        limit: int = 10,
        min_similarity: float = 0.3
    ) -> List[MemoryEntry]:
        """Retrieve memories relevant to a query using semantic similarity."""
        
        query_embedding = self._get_embedding(query)
        relevant_memories = []
        
        # Get embeddings for the specified memory type
        embedding_key = self._create_embedding_key(user_id, memory_type)
        embedding_data_list = self.redis_client.lrange(embedding_key, 0, -1)
        
        similarities = []
        
        for embedding_data_str in embedding_data_list:
            try:
                embedding_data = json.loads(embedding_data_str)
                memory_embedding = np.array(embedding_data['embedding'])
                
                # Calculate similarity
                similarity = cosine_similarity(
                    [query_embedding], [memory_embedding]
                )[0][0]
                
                # Apply importance/relevance weighting
                weight = embedding_data.get('importance', 0.5)
                if memory_type == MemoryType.UNIVERSAL:
                    weight = embedding_data.get('global_relevance', 0.5)
                
                weighted_similarity = similarity * weight
                
                if weighted_similarity >= min_similarity:
                    similarities.append((embedding_data['memory_id'], weighted_similarity))
                    
            except (json.JSONDecodeError, KeyError) as e:
                continue
        
        # Sort by similarity and get top results
        similarities.sort(key=lambda x: x[1], reverse=True)
        top_memory_ids = [mem_id for mem_id, _ in similarities[:limit]]
        
        # Retrieve full memory objects
        for memory_id in top_memory_ids:
            memory_key = self._create_memory_key(user_id, memory_type, memory_id)
            memory_data = self.redis_client.get(memory_key)
            
            if memory_data:
                try:
                    memory_dict = json.loads(memory_data)
                    if memory_type == MemoryType.PERSONAL:
                        memory = PersonalMemory(**memory_dict)
                    else:
                        memory = UniversalMemory(**memory_dict)
                    
                    # Update access statistics
                    memory.last_accessed = datetime.now(timezone.utc)
                    memory.access_count += 1
                    
                    # Store updated memory
                    self.redis_client.setex(
                        memory_key,
                        self.memory_ttl,
                        memory.json()
                    )
                    
                    relevant_memories.append(memory)
                    
                except Exception as e:
                    continue
        
        return relevant_memories
    
    def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Get user profile from persistent storage."""
        return self.storage_service.retrieve_user_profile(user_id)
    
    def create_user_profile(self, user_id: str, name: Optional[str] = None) -> UserProfile:
        """Create a new user profile."""
        profile = UserProfile(
            user_id=user_id,
            name=name
        )

        self.storage_service.store_user_profile(profile)
        return profile
    
    def _update_user_profile(
        self,
        user_id: str,
        interaction_type: InteractionType,
        emotion: Optional[EmotionType] = None
    ):
        """Update user profile based on interaction."""
        profile = self.get_user_profile(user_id)
        
        if not profile:
            profile = self.create_user_profile(user_id)
        
        # Update interaction count
        profile.interaction_count += 1
        profile.last_interaction = datetime.now(timezone.utc)
        
        # Update emotional patterns
        if emotion:
            current_weight = profile.emotional_patterns.get(emotion, 0.0)
            profile.emotional_patterns[emotion] = current_weight + settings.learning_rate
        
        # Normalize emotional patterns
        total_weight = sum(profile.emotional_patterns.values())
        if total_weight > 0:
            for emotion_type in profile.emotional_patterns:
                profile.emotional_patterns[emotion_type] /= total_weight
        
        # Store updated profile
        profile_key = self._create_user_profile_key(user_id)
        self.redis_client.setex(
            profile_key,
            self.memory_ttl,
            profile.json()
        )
    
    def _find_similar_universal_memory(self, content: str, similarity_threshold: float = 0.8) -> Optional[UniversalMemory]:
        """Find similar universal memory to avoid duplicates."""
        content_embedding = self._get_embedding(content)
        embedding_key = self._create_embedding_key("", MemoryType.UNIVERSAL)
        embedding_data_list = self.redis_client.lrange(embedding_key, 0, -1)
        
        for embedding_data_str in embedding_data_list:
            try:
                embedding_data = json.loads(embedding_data_str)
                memory_embedding = np.array(embedding_data['embedding'])
                
                similarity = cosine_similarity(
                    [content_embedding], [memory_embedding]
                )[0][0]
                
                if similarity >= similarity_threshold:
                    # Retrieve the full memory
                    memory_key = self._create_memory_key("", MemoryType.UNIVERSAL, embedding_data['memory_id'])
                    memory_data = self.redis_client.get(memory_key)
                    
                    if memory_data:
                        memory_dict = json.loads(memory_data)
                        return UniversalMemory(**memory_dict)
                        
            except (json.JSONDecodeError, KeyError):
                continue
        
        return None
    
    def create_contextual_memory(
        self,
        conversation_id: str,
        user_id: str,
        query: str
    ) -> ContextualMemory:
        """Create contextual memory for a conversation."""
        
        # Retrieve relevant personal and universal memories
        personal_memories = self.retrieve_relevant_memories(
            user_id, query, MemoryType.PERSONAL, limit=5
        )
        
        universal_memories = self.retrieve_relevant_memories(
            user_id, query, MemoryType.UNIVERSAL, limit=3
        )
        
        # Combine memories
        relevant_memories = personal_memories + universal_memories
        
        # Analyze emotional context
        emotional_context = {}
        for memory in relevant_memories:
            if memory.emotion:
                current_weight = emotional_context.get(memory.emotion, 0.0)
                emotional_context[memory.emotion] = current_weight + memory.importance
        
        # Normalize emotional context
        total_weight = sum(emotional_context.values())
        if total_weight > 0:
            for emotion in emotional_context:
                emotional_context[emotion] /= total_weight
        
        # Extract topic context
        topic_context = []
        for memory in relevant_memories:
            if hasattr(memory, 'topic_tags'):
                topic_context.extend(memory.topic_tags)
        
        # Remove duplicates and limit
        topic_context = list(set(topic_context))[:10]
        
        # Create contextual memory
        contextual_memory = ContextualMemory(
            conversation_id=conversation_id,
            user_id=user_id,
            relevant_memories=relevant_memories,
            emotional_context=emotional_context,
            topic_context=topic_context
        )
        
        return contextual_memory
    
    def cleanup_expired_memories(self):
        """Clean up expired memories (Redis handles TTL automatically)."""
        # This is mainly for logging and monitoring
        # Redis automatically removes expired keys
        pass
    
    def get_memory_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get memory statistics for a user."""
        personal_count = len(self.redis_client.keys(f"memory:personal:{user_id}:*"))
        universal_count = len(self.redis_client.keys("memory:universal:*"))
        
        profile = self.get_user_profile(user_id)
        
        return {
            "personal_memories": personal_count,
            "universal_memories": universal_count,
            "total_interactions": profile.interaction_count if profile else 0,
            "last_interaction": profile.last_interaction.isoformat() if profile else None,
            "emotional_patterns": profile.emotional_patterns if profile else {}
        }

# Import the utility function
from models import calculate_memory_importance 