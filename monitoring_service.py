"""
Production-grade monitoring and error handling service for the AI Companion System.
Provides comprehensive logging, metrics, health checks, and error tracking.
"""

import asyncio
import logging
import time
import traceback
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import threading
from collections import defaultdict, deque
import json

from config import settings

class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class HealthStatus(Enum):
    """System health status."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"

@dataclass
class MetricPoint:
    """Individual metric data point."""
    name: str
    value: float
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class Alert:
    """System alert."""
    level: AlertLevel
    message: str
    component: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class HealthCheck:
    """Health check result."""
    component: str
    status: HealthStatus
    message: str
    response_time: float
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: Dict[str, Any] = field(default_factory=dict)

class MonitoringService:
    """Comprehensive monitoring and alerting service."""
    
    def __init__(self):
        """Initialize the monitoring service."""
        self.logger = self._setup_logging()
        
        # Metrics storage (in-memory for now, could be extended to external systems)
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.alerts: deque = deque(maxlen=500)
        self.health_checks: Dict[str, HealthCheck] = {}
        
        # Performance tracking
        self.request_times: deque = deque(maxlen=1000)
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.active_connections: int = 0
        
        # Rate limiting tracking
        self.rate_limit_buckets: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Background monitoring
        self._start_background_monitoring()
    
    def _setup_logging(self) -> logging.Logger:
        """Setup production-grade logging."""
        logger = logging.getLogger('ai_companion')
        logger.setLevel(getattr(logging, settings.log_level))
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # File handler for production
        if settings.environment == 'production':
            file_handler = logging.FileHandler('ai_companion.log')
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """Record a metric data point."""
        metric = MetricPoint(name=name, value=value, tags=tags or {})
        self.metrics[name].append(metric)
        
        # Log significant metrics
        if name in ['response_time', 'error_rate', 'memory_usage']:
            self.logger.debug(f"Metric {name}: {value}")
    
    def record_request_time(self, duration: float, endpoint: str = None):
        """Record request processing time."""
        self.request_times.append(duration)
        self.record_metric('response_time', duration, {'endpoint': endpoint or 'unknown'})
        
        # Alert on slow requests
        if duration > 5.0:  # 5 seconds
            self.create_alert(
                AlertLevel.WARNING,
                f"Slow request detected: {duration:.2f}s",
                'performance',
                {'duration': duration, 'endpoint': endpoint}
            )
    
    def record_error(self, error: Exception, component: str, context: Dict[str, Any] = None):
        """Record an error with full context."""
        error_key = f"{component}:{type(error).__name__}"
        self.error_counts[error_key] += 1
        
        # Create detailed error log
        error_details = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'component': component,
            'context': context or {},
            'traceback': traceback.format_exc()
        }
        
        self.logger.error(f"Error in {component}: {error}", extra=error_details)
        
        # Create alert for critical errors
        if isinstance(error, (ConnectionError, TimeoutError)):
            self.create_alert(
                AlertLevel.CRITICAL,
                f"Critical error in {component}: {error}",
                component,
                error_details
            )
        else:
            self.create_alert(
                AlertLevel.ERROR,
                f"Error in {component}: {error}",
                component,
                error_details
            )
    
    def create_alert(self, level: AlertLevel, message: str, component: str, metadata: Dict[str, Any] = None):
        """Create a system alert."""
        alert = Alert(
            level=level,
            message=message,
            component=component,
            metadata=metadata or {}
        )
        
        self.alerts.append(alert)
        
        # Log alert
        log_level = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }[level]
        
        self.logger.log(log_level, f"ALERT [{level.value.upper()}] {component}: {message}")
    
    def check_health(self, component: str, check_func: Callable[[], bool], timeout: float = 5.0) -> HealthCheck:
        """Perform a health check on a component."""
        start_time = time.time()
        
        try:
            # Run health check with timeout
            result = asyncio.wait_for(
                asyncio.create_task(self._run_health_check(check_func)),
                timeout=timeout
            )
            
            response_time = time.time() - start_time
            
            if result:
                status = HealthStatus.HEALTHY
                message = "Component is healthy"
            else:
                status = HealthStatus.DEGRADED
                message = "Component check failed"
                
        except asyncio.TimeoutError:
            response_time = timeout
            status = HealthStatus.UNHEALTHY
            message = f"Health check timed out after {timeout}s"
            
        except Exception as e:
            response_time = time.time() - start_time
            status = HealthStatus.UNHEALTHY
            message = f"Health check error: {e}"
        
        health_check = HealthCheck(
            component=component,
            status=status,
            message=message,
            response_time=response_time
        )
        
        self.health_checks[component] = health_check
        return health_check
    
    async def _run_health_check(self, check_func: Callable[[], bool]) -> bool:
        """Run health check function asynchronously."""
        return check_func()
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status."""
        overall_status = HealthStatus.HEALTHY
        
        # Check individual components
        for health_check in self.health_checks.values():
            if health_check.status == HealthStatus.UNHEALTHY:
                overall_status = HealthStatus.UNHEALTHY
                break
            elif health_check.status == HealthStatus.DEGRADED:
                overall_status = HealthStatus.DEGRADED
        
        # Calculate metrics
        avg_response_time = sum(self.request_times) / len(self.request_times) if self.request_times else 0
        error_rate = sum(self.error_counts.values()) / max(len(self.request_times), 1)
        
        return {
            'overall_status': overall_status.value,
            'components': {name: check.status.value for name, check in self.health_checks.items()},
            'metrics': {
                'average_response_time': avg_response_time,
                'error_rate': error_rate,
                'active_connections': self.active_connections,
                'total_requests': len(self.request_times)
            },
            'recent_alerts': [
                {
                    'level': alert.level.value,
                    'message': alert.message,
                    'component': alert.component,
                    'timestamp': alert.timestamp.isoformat()
                }
                for alert in list(self.alerts)[-10:]  # Last 10 alerts
            ]
        }
    
    def check_rate_limit(self, user_id: str) -> bool:
        """Check if user is within rate limits."""
        now = time.time()
        window_start = now - settings.rate_limit_window
        
        # Clean old entries
        user_bucket = self.rate_limit_buckets[user_id]
        while user_bucket and user_bucket[0] < window_start:
            user_bucket.popleft()
        
        # Check current count
        if len(user_bucket) >= settings.rate_limit_requests:
            self.create_alert(
                AlertLevel.WARNING,
                f"Rate limit exceeded for user {user_id}",
                'rate_limiting',
                {'user_id': user_id, 'requests': len(user_bucket)}
            )
            return False
        
        # Add current request
        user_bucket.append(now)
        return True
    
    def _start_background_monitoring(self):
        """Start background monitoring tasks."""
        def monitoring_worker():
            while True:
                try:
                    # Perform periodic health checks
                    self._periodic_health_checks()
                    
                    # Clean old metrics
                    self._cleanup_old_data()
                    
                    # Check for anomalies
                    self._detect_anomalies()
                    
                    time.sleep(settings.health_check_interval)
                    
                except Exception as e:
                    self.logger.error(f"Background monitoring error: {e}")
                    time.sleep(60)  # Wait longer on error
        
        # Start background thread
        monitoring_thread = threading.Thread(target=monitoring_worker, daemon=True)
        monitoring_thread.start()
    
    def _periodic_health_checks(self):
        """Perform periodic health checks."""
        # Check Redis connectivity
        try:
            from storage_service import PersistentStorageService
            storage = PersistentStorageService()
            if storage.redis_available:
                storage.redis_client.ping()
                self.health_checks['redis'] = HealthCheck(
                    component='redis',
                    status=HealthStatus.HEALTHY,
                    message='Redis is responsive',
                    response_time=0.1
                )
        except Exception as e:
            self.health_checks['redis'] = HealthCheck(
                component='redis',
                status=HealthStatus.UNHEALTHY,
                message=f'Redis error: {e}',
                response_time=5.0
            )
    
    def _cleanup_old_data(self):
        """Clean up old monitoring data."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
        
        # Clean old alerts
        self.alerts = deque(
            [alert for alert in self.alerts if alert.timestamp > cutoff_time],
            maxlen=500
        )
    
    def _detect_anomalies(self):
        """Detect system anomalies."""
        # Check error rate
        if self.request_times:
            recent_errors = sum(1 for _ in self.request_times if _ > 2.0)  # Requests > 2s
            error_rate = recent_errors / len(self.request_times)
            
            if error_rate > 0.1:  # 10% error rate
                self.create_alert(
                    AlertLevel.WARNING,
                    f"High error rate detected: {error_rate:.2%}",
                    'anomaly_detection',
                    {'error_rate': error_rate}
                )

# Global monitoring instance
monitoring = MonitoringService()

def monitor_performance(func):
    """Decorator to monitor function performance."""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            monitoring.record_request_time(duration, func.__name__)
            return result
        except Exception as e:
            duration = time.time() - start_time
            monitoring.record_request_time(duration, func.__name__)
            monitoring.record_error(e, func.__name__)
            raise
    return wrapper

def log_error(component: str):
    """Decorator to log errors with context."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                monitoring.record_error(e, component, {
                    'function': func.__name__,
                    'args': str(args)[:200],  # Truncate for privacy
                    'kwargs': str(kwargs)[:200]
                })
                raise
        return wrapper
    return decorator
