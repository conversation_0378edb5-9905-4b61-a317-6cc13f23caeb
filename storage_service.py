"""
Persistent Storage Service for the AI Companion System.
Provides robust, production-grade data persistence with backup and recovery.
"""

import asyncio
import json
import logging
import sqlite3
import threading
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import redis
from sqlalchemy import create_engine, Column, String, Text, DateTime, Integer, Float, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.sqlite import JSON
import pickle
import gzip

from models import (
    MemoryEntry, PersonalMemory, UniversalMemory, UserProfile, 
    ContextualMemory, MemoryType, EmotionType, utc_now
)
from config import settings

# SQLAlchemy Base
Base = declarative_base()

class StoredMemory(Base):
    """SQLAlchemy model for persistent memory storage."""
    __tablename__ = 'memories'
    
    id = Column(String, primary_key=True)
    user_id = Column(String, index=True)
    memory_type = Column(String, index=True)
    content = Column(Text)
    memory_metadata = Column(JSON)  # Store all other fields as JSON
    created_at = Column(DateTime, default=utc_now)
    last_accessed = Column(DateTime, default=utc_now)
    access_count = Column(Integer, default=0)
    importance = Column(Float, default=0.5)
    embedding = Column(Text)  # Store as compressed JSON

class StoredProfile(Base):
    """SQLAlchemy model for user profiles."""
    __tablename__ = 'user_profiles'
    
    user_id = Column(String, primary_key=True)
    name = Column(String)
    preferences = Column(JSON)
    interests = Column(JSON)
    communication_style = Column(JSON)
    emotional_patterns = Column(JSON)
    routines = Column(JSON)
    goals = Column(JSON)
    created_at = Column(DateTime, default=utc_now)
    last_interaction = Column(DateTime, default=utc_now)
    interaction_count = Column(Integer, default=0)

class StoredConversation(Base):
    """SQLAlchemy model for conversation persistence."""
    __tablename__ = 'conversations'
    
    id = Column(String, primary_key=True)
    user_id = Column(String, index=True)
    messages = Column(Text)  # Compressed JSON
    start_time = Column(DateTime, default=utc_now)
    end_time = Column(DateTime)
    summary = Column(Text)
    topics = Column(JSON)
    emotions = Column(JSON)
    is_active = Column(Boolean, default=True)

class PersistentStorageService:
    """Production-grade persistent storage with Redis caching."""
    
    def __init__(self):
        """Initialize the storage service."""
        self.logger = logging.getLogger(__name__)
        
        # Initialize SQLAlchemy
        self.engine = create_engine(
            settings.database_url,
            echo=settings.debug_mode,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        Base.metadata.create_all(self.engine)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # Initialize Redis for caching
        try:
            self.redis_client = redis.from_url(
                settings.redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            self.redis_available = True
            self.redis_client.ping()
            self.logger.info("Redis connection established")
        except Exception as e:
            self.logger.warning(f"Redis unavailable, using database only: {e}")
            self.redis_available = False
        
        # Cache settings
        self.cache_ttl = 3600  # 1 hour
        self.memory_cache_ttl = 7200  # 2 hours for memories
        
        # Background sync
        self._start_background_sync()
    
    def get_db_session(self) -> Session:
        """Get database session with proper error handling."""
        return self.SessionLocal()
    
    def store_memory(self, memory: Union[PersonalMemory, UniversalMemory]) -> bool:
        """Store memory with dual persistence (Redis + SQLite)."""
        try:
            # Store in database
            with self.get_db_session() as db:
                stored_memory = StoredMemory(
                    id=memory.id,
                    user_id=memory.user_id,
                    memory_type=memory.memory_type.value,
                    content=memory.content,
                    memory_metadata=self._serialize_memory_metadata(memory),
                    created_at=memory.created_at,
                    last_accessed=memory.last_accessed,
                    access_count=memory.access_count,
                    importance=memory.importance,
                    embedding=self._compress_embedding(getattr(memory, 'embedding', None))
                )
                
                db.merge(stored_memory)  # Use merge for upsert
                db.commit()
            
            # Cache in Redis if available
            if self.redis_available:
                cache_key = f"memory:{memory.memory_type.value}:{memory.user_id}:{memory.id}"
                self.redis_client.setex(
                    cache_key,
                    self.memory_cache_ttl,
                    memory.json()
                )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store memory {memory.id}: {e}")
            return False
    
    def retrieve_memory(self, memory_id: str, user_id: str, memory_type: MemoryType) -> Optional[Union[PersonalMemory, UniversalMemory]]:
        """Retrieve memory with cache-first strategy."""
        try:
            # Try cache first
            if self.redis_available:
                cache_key = f"memory:{memory_type.value}:{user_id}:{memory_id}"
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    memory_dict = json.loads(cached_data)
                    if memory_type == MemoryType.PERSONAL:
                        return PersonalMemory(**memory_dict)
                    else:
                        return UniversalMemory(**memory_dict)
            
            # Fallback to database
            with self.get_db_session() as db:
                stored = db.query(StoredMemory).filter(
                    StoredMemory.id == memory_id,
                    StoredMemory.user_id == user_id,
                    StoredMemory.memory_type == memory_type.value
                ).first()
                
                if stored:
                    memory = self._deserialize_memory(stored)
                    
                    # Update cache
                    if self.redis_available:
                        cache_key = f"memory:{memory_type.value}:{user_id}:{memory_id}"
                        self.redis_client.setex(
                            cache_key,
                            self.memory_cache_ttl,
                            memory.json()
                        )
                    
                    return memory
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve memory {memory_id}: {e}")
            return None
    
    def store_user_profile(self, profile: UserProfile) -> bool:
        """Store user profile with persistence."""
        try:
            with self.get_db_session() as db:
                stored_profile = StoredProfile(
                    user_id=profile.user_id,
                    name=profile.name,
                    preferences=profile.preferences,
                    interests=profile.interests,
                    communication_style=profile.communication_style,
                    emotional_patterns={k.value: v for k, v in profile.emotional_patterns.items()},
                    routines=profile.routines,
                    goals=profile.goals,
                    created_at=profile.created_at,
                    last_interaction=profile.last_interaction,
                    interaction_count=profile.interaction_count
                )
                
                db.merge(stored_profile)
                db.commit()
            
            # Cache in Redis
            if self.redis_available:
                cache_key = f"profile:{profile.user_id}"
                self.redis_client.setex(
                    cache_key,
                    self.cache_ttl,
                    profile.json()
                )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store profile {profile.user_id}: {e}")
            return False
    
    def retrieve_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Retrieve user profile with cache-first strategy."""
        try:
            # Try cache first
            if self.redis_available:
                cache_key = f"profile:{user_id}"
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    return UserProfile(**json.loads(cached_data))
            
            # Fallback to database
            with self.get_db_session() as db:
                stored = db.query(StoredProfile).filter(
                    StoredProfile.user_id == user_id
                ).first()
                
                if stored:
                    profile = UserProfile(
                        user_id=stored.user_id,
                        name=stored.name,
                        preferences=stored.preferences or {},
                        interests=stored.interests or [],
                        communication_style=stored.communication_style or {},
                        emotional_patterns={EmotionType(k): v for k, v in (stored.emotional_patterns or {}).items()},
                        routines=stored.routines or [],
                        goals=stored.goals or [],
                        created_at=stored.created_at,
                        last_interaction=stored.last_interaction,
                        interaction_count=stored.interaction_count
                    )
                    
                    # Update cache
                    if self.redis_available:
                        cache_key = f"profile:{user_id}"
                        self.redis_client.setex(
                            cache_key,
                            self.cache_ttl,
                            profile.json()
                        )
                    
                    return profile
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve profile {user_id}: {e}")
            return None
    
    def store_conversation(self, conversation_id: str, user_id: str, messages: List[Dict], 
                          summary: str = None, topics: List[str] = None, 
                          emotions: List[str] = None) -> bool:
        """Store conversation with compression."""
        try:
            with self.get_db_session() as db:
                # Compress messages
                compressed_messages = gzip.compress(
                    json.dumps(messages).encode('utf-8')
                ).decode('latin-1')
                
                stored_conversation = StoredConversation(
                    id=conversation_id,
                    user_id=user_id,
                    messages=compressed_messages,
                    summary=summary,
                    topics=topics or [],
                    emotions=emotions or [],
                    end_time=utc_now() if summary else None  # Mark as ended if summary provided
                )
                
                db.merge(stored_conversation)
                db.commit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store conversation {conversation_id}: {e}")
            return False
    
    def retrieve_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve conversation with decompression."""
        try:
            with self.get_db_session() as db:
                stored = db.query(StoredConversation).filter(
                    StoredConversation.id == conversation_id
                ).first()
                
                if stored:
                    # Decompress messages
                    messages = json.loads(
                        gzip.decompress(stored.messages.encode('latin-1')).decode('utf-8')
                    )
                    
                    return {
                        'id': stored.id,
                        'user_id': stored.user_id,
                        'messages': messages,
                        'start_time': stored.start_time,
                        'end_time': stored.end_time,
                        'summary': stored.summary,
                        'topics': stored.topics,
                        'emotions': stored.emotions,
                        'is_active': stored.is_active
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve conversation {conversation_id}: {e}")
            return None
    
    def _serialize_memory_metadata(self, memory: Union[PersonalMemory, UniversalMemory]) -> Dict[str, Any]:
        """Serialize memory metadata for storage."""
        metadata = {
            'interaction_type': memory.interaction_type.value,
            'context': memory.context,
            'emotion': memory.emotion.value if memory.emotion else None,
            'confidence': memory.confidence
        }
        
        if isinstance(memory, PersonalMemory):
            metadata.update({
                'privacy_level': memory.privacy_level,
                'relationship_context': memory.relationship_context,
                'personal_goals': memory.personal_goals
            })
        elif isinstance(memory, UniversalMemory):
            metadata.update({
                'topic_tags': memory.topic_tags,
                'global_relevance': memory.global_relevance,
                'source_count': memory.source_count
            })
        
        return metadata
    
    def _deserialize_memory(self, stored: StoredMemory) -> Union[PersonalMemory, UniversalMemory]:
        """Deserialize memory from storage."""
        base_data = {
            'id': stored.id,
            'user_id': stored.user_id,
            'memory_type': MemoryType(stored.memory_type),
            'content': stored.content,
            'created_at': stored.created_at,
            'last_accessed': stored.last_accessed,
            'access_count': stored.access_count,
            'importance': stored.importance,
            'context': stored.memory_metadata.get('context', {}),
            'confidence': stored.memory_metadata.get('confidence', 1.0)
        }

        # Add emotion if present
        if stored.memory_metadata.get('emotion'):
            base_data['emotion'] = EmotionType(stored.memory_metadata['emotion'])

        # Add interaction type
        if stored.memory_metadata.get('interaction_type'):
            from models import InteractionType
            base_data['interaction_type'] = InteractionType(stored.memory_metadata['interaction_type'])
        
        if stored.memory_type == MemoryType.PERSONAL.value:
            base_data.update({
                'privacy_level': stored.memory_metadata.get('privacy_level', 'private'),
                'relationship_context': stored.memory_metadata.get('relationship_context'),
                'personal_goals': stored.memory_metadata.get('personal_goals', [])
            })
            return PersonalMemory(**base_data)
        else:
            base_data.update({
                'topic_tags': stored.memory_metadata.get('topic_tags', []),
                'global_relevance': stored.memory_metadata.get('global_relevance', 0.5),
                'source_count': stored.memory_metadata.get('source_count', 1)
            })
            return UniversalMemory(**base_data)
    
    def _compress_embedding(self, embedding: Optional[List[float]]) -> Optional[str]:
        """Compress embedding vector for storage."""
        if embedding is None:
            return None
        
        try:
            # Convert to bytes and compress
            embedding_bytes = pickle.dumps(embedding)
            compressed = gzip.compress(embedding_bytes)
            return compressed.hex()
        except Exception:
            return None
    
    def _decompress_embedding(self, compressed_embedding: Optional[str]) -> Optional[List[float]]:
        """Decompress embedding vector from storage."""
        if compressed_embedding is None:
            return None
        
        try:
            # Decompress and convert back
            compressed_bytes = bytes.fromhex(compressed_embedding)
            embedding_bytes = gzip.decompress(compressed_bytes)
            return pickle.loads(embedding_bytes)
        except Exception:
            return None
    
    def _start_background_sync(self):
        """Start background synchronization tasks."""
        def sync_worker():
            while True:
                try:
                    # Sync Redis to database every 5 minutes
                    asyncio.sleep(300)
                    self._sync_redis_to_database()
                except Exception as e:
                    self.logger.error(f"Background sync error: {e}")
        
        # Start background thread
        sync_thread = threading.Thread(target=sync_worker, daemon=True)
        sync_thread.start()
    
    def _sync_redis_to_database(self):
        """Sync Redis cache to database for persistence."""
        if not self.redis_available:
            return
        
        try:
            # This would implement periodic sync of cached data to database
            # For now, we rely on immediate dual-write strategy
            pass
        except Exception as e:
            self.logger.error(f"Redis sync error: {e}")
    
    def cleanup_old_data(self, days: int = 90):
        """Clean up old data based on retention policy."""
        try:
            cutoff_date = utc_now() - timedelta(days=days)
            
            with self.get_db_session() as db:
                # Clean up old conversations
                db.query(StoredConversation).filter(
                    StoredConversation.end_time < cutoff_date,
                    StoredConversation.is_active == False
                ).delete()
                
                # Clean up old memories with low importance
                db.query(StoredMemory).filter(
                    StoredMemory.last_accessed < cutoff_date,
                    StoredMemory.importance < 0.3,
                    StoredMemory.access_count < 2
                ).delete()
                
                db.commit()
                
            self.logger.info(f"Cleaned up data older than {days} days")
            
        except Exception as e:
            self.logger.error(f"Data cleanup error: {e}")
