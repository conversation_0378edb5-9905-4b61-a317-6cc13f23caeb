# AI Companion System Environment Configuration

# Required: Google Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: OpenAI API Key (for additional features)
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
REDIS_URL=redis://localhost:6379
DATABASE_URL=sqlite:///./ai_companion.db

# Memory Configuration
MEMORY_TTL=2592000
PERSONAL_MEMORY_SIZE=1000
UNIVERSAL_MEMORY_SIZE=10000

# Learning Configuration
LEARNING_RATE=0.1
ADAPTATION_THRESHOLD=5
EMOTION_WEIGHT=0.3

# Conversation Configuration
MAX_CONTEXT_LENGTH=2000
MAX_HISTORY_LENGTH=50
RESPONSE_TEMPERATURE=0.7
MAX_TOKENS=150

# System Configuration
LOG_LEVEL=INFO
ENVIRONMENT=development
DEBUG_MODE=true

# Security Configuration
SECRET_KEY=dev-secret-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring & Health Checks
HEALTH_CHECK_INTERVAL=30
METRICS_ENABLED=true

# Service Ports
GRADIO_PORT=7860
API_PORT=8000
MEMORY_SERVICE_PORT=8001
LEARNING_SERVICE_PORT=8002
