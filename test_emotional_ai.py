"""
Test the enhanced emotional intelligence system with real Gemini API.
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

def test_emotional_ai():
    """Test the emotional AI system."""
    print("🧠 Testing Enhanced Emotional AI System")
    print("=" * 50)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from models import EmotionType, EmotionalState
        from emotional_intelligence_service import EmotionalIntelligenceService
        from gemini_service import GeminiService
        print("✅ All modules imported successfully")
        
        # Initialize services
        print("\n🔧 Initializing services...")
        gemini_service = GeminiService()
        ei_service = EmotionalIntelligenceService(gemini_service)
        print("✅ Services initialized")
        
        # Test emotion analysis with real API
        print("\n🎭 Testing real emotion analysis...")
        test_messages = [
            "I'm so excited! I just got my dream job!",
            "I'm feeling really anxious about tomorrow...",
            "I'm devastated about my grandmother passing away.",
            "Just a normal day, nothing special.",
            "I'm frustrated with this computer!"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n--- Test {i} ---")
            print(f"Message: {message}")
            
            # Analyze emotion
            emotional_state = ei_service.analyze_advanced_emotion(
                text=message,
                user_id=f"test_user_{i}"
            )
            
            print(f"🎭 Emotion: {emotional_state.primary_emotion.value}")
            print(f"💪 Intensity: {emotional_state.intensity:.2f}")
            print(f"😊 Valence: {emotional_state.valence:.2f}")
            print(f"🎯 Confidence: {emotional_state.confidence:.2f}")
            
            # Create empathy model
            empathy_model = ei_service.create_empathy_model(
                user_id=f"test_user_{i}",
                emotional_state=emotional_state,
                conversation_history=[{"role": "user", "content": message}]
            )
            
            print(f"🤗 Strategy: {empathy_model.response_strategy}")
            print(f"💝 Support needed: {empathy_model.should_provide_support()}")
            
            # Generate empathetic response
            response = ei_service.generate_empathetic_response(
                empathy_model=empathy_model,
                conversation_history=[{"role": "user", "content": message}],
                user_message=message
            )
            
            print(f"🤖 Response: {response}")
        
        print("\n🎉 All tests completed successfully!")
        print("✅ Enhanced Emotional AI System is working with real Gemini API!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_emotional_ai()
    if success:
        print("\n🚀 System is ready for emotional conversations!")
    else:
        print("\n⚠️  Please check the errors and try again.")
        sys.exit(1)
