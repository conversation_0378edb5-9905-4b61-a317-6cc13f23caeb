"""
Simple test to verify the enhanced emotional intelligence system.
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

def test_emotional_intelligence():
    """Test the emotional intelligence service."""
    print("🧠 Testing Enhanced Emotional Intelligence System")
    print("=" * 50)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from models import EmotionType, EmotionalState, EmpathyModel
        print("✅ Models imported successfully")
        
        from emotional_intelligence_service import EmotionalIntelligenceService
        print("✅ Emotional Intelligence Service imported successfully")
        
        # Create a mock Gemini service for testing
        class MockGeminiService:
            def __init__(self):
                self.model = self
            
            def generate_content(self, prompt):
                # Mock response for emotion analysis
                if "emotional content" in prompt.lower():
                    return type('Response', (), {
                        'text': '''
                        {
                            "primary_emotion": "joy",
                            "secondary_emotions": {"excitement": 0.6, "contentment": 0.4},
                            "intensity": 0.8,
                            "confidence": 0.9,
                            "valence": 0.7,
                            "arousal": 0.6,
                            "context_factors": ["positive news", "achievement"],
                            "emotional_triggers": ["success", "recognition"],
                            "support_needed": false,
                            "reasoning": "The text expresses clear joy and excitement about a positive achievement."
                        }
                        '''
                    })()
                else:
                    # Mock empathetic response
                    return type('Response', (), {
                        'text': "I can hear the excitement in your message! That's wonderful news about your promotion. You must be feeling really proud of your hard work paying off. How are you planning to celebrate this achievement?"
                    })()
        
        # Initialize services
        mock_gemini = MockGeminiService()
        ei_service = EmotionalIntelligenceService(mock_gemini)
        print("✅ Services initialized successfully")
        
        # Test emotion analysis
        print("\n🎭 Testing emotion analysis...")
        test_message = "I'm so excited! I just got promoted at work and I can't believe it!"
        
        emotional_state = ei_service.analyze_advanced_emotion(
            text=test_message,
            user_id="test_user_123"
        )
        
        print(f"✅ Primary emotion detected: {emotional_state.primary_emotion.value}")
        print(f"✅ Emotional intensity: {emotional_state.intensity}")
        print(f"✅ Emotional valence: {emotional_state.valence}")
        print(f"✅ Confidence: {emotional_state.confidence}")
        
        # Test empathy model creation
        print("\n🤗 Testing empathy model creation...")
        empathy_model = ei_service.create_empathy_model(
            user_id="test_user_123",
            emotional_state=emotional_state,
            conversation_history=[
                {"role": "user", "content": test_message},
            ]
        )
        
        print(f"✅ Response strategy: {empathy_model.response_strategy}")
        print(f"✅ Empathy level: {empathy_model.empathy_level}")
        print(f"✅ Support needed: {empathy_model.should_provide_support()}")
        print(f"✅ Comfort techniques: {', '.join(empathy_model.comfort_techniques)}")
        
        # Test empathetic response generation
        print("\n💬 Testing empathetic response generation...")
        response = ei_service.generate_empathetic_response(
            empathy_model=empathy_model,
            conversation_history=[
                {"role": "user", "content": test_message},
            ],
            user_message=test_message
        )
        
        print(f"✅ Generated empathetic response:")
        print(f"   {response}")
        
        # Test emotional insights
        print("\n📊 Testing emotional insights...")
        insights = ei_service.get_emotional_insights("test_user_123")
        print(f"✅ Total interactions: {insights.get('total_interactions', 0)}")
        print(f"✅ Average valence: {insights.get('average_valence', 0)}")
        print(f"✅ Emotional stability: {insights.get('emotional_stability', 0)}")
        
        print("\n🎉 All tests passed! Enhanced Emotional Intelligence System is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_emotional_intelligence()
    if success:
        print("\n✅ Enhanced Emotional Intelligence System is ready!")
    else:
        print("\n❌ There are issues that need to be resolved.")
        sys.exit(1)
