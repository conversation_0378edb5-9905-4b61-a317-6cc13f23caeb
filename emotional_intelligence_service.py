"""
Advanced Emotional Intelligence Service for the AI Companion System.
Provides sophisticated emotion detection, empathy modeling, and emotional support.
"""

import json
import re
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, deque

from models import (
    EmotionType, InteractionType, EmotionalState, EmpathyModel, 
    EmotionalMemory, MemoryEntry, utc_now
)
from gemini_service import GeminiService
from config import settings

class EmotionalIntelligenceService:
    """Advanced emotional intelligence and empathy service."""
    
    def __init__(self, gemini_service: GeminiService):
        """Initialize the emotional intelligence service."""
        self.gemini_service = gemini_service
        
        # Emotional state tracking
        self.user_emotional_histories: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=50)  # Keep last 50 emotional states
        )
        
        # Empathy models per user
        self.user_empathy_models: Dict[str, EmpathyModel] = {}
        
        # Emotional patterns and triggers
        self.emotional_patterns: Dict[str, Dict] = defaultdict(dict)
        
        # Enhanced emotion detection prompts
        self.advanced_emotion_prompt = """
        Analyze the emotional content of this text with advanced psychological understanding.
        Consider context, subtext, and emotional complexity.
        
        Return a JSON response with:
        - primary_emotion: main emotion (joy, sadness, anger, fear, surprise, disgust, neutral, excitement, anxiety, contentment, frustration, hope, disappointment, gratitude, loneliness, confusion, pride, shame, love, stress, relief, curiosity, boredom, overwhelm)
        - secondary_emotions: dict of additional emotions with weights (0-1)
        - intensity: emotional intensity (0-1)
        - confidence: confidence in analysis (0-1)
        - valence: emotional valence from negative (-1) to positive (1)
        - arousal: emotional arousal from calm (-1) to excited (1)
        - context_factors: list of contextual factors affecting emotion
        - emotional_triggers: list of potential emotional triggers
        - support_needed: boolean indicating if emotional support is needed
        - reasoning: detailed explanation of emotional analysis
        
        Text: {text}
        Context: {context}
        """
        
        self.empathy_response_prompt = """
        Generate an empathetic response based on the user's emotional state and history.
        You are a caring, understanding AI companion who provides emotional support.
        
        User Emotional State: {emotional_state}
        Conversation History: {conversation_history}
        Empathy Strategy: {empathy_strategy}
        Support Techniques: {support_techniques}
        
        Guidelines:
        1. Acknowledge and validate the user's emotions
        2. Show genuine understanding and care
        3. Provide appropriate emotional support
        4. Use warm, human-like language
        5. Avoid being overly clinical or robotic
        6. Match the emotional tone appropriately
        7. Offer comfort, encouragement, or celebration as needed
        
        Generate a natural, empathetic response:
        """
    
    def analyze_advanced_emotion(
        self, 
        text: str, 
        context: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None
    ) -> EmotionalState:
        """Perform advanced emotion analysis with psychological depth."""
        try:
            # Prepare context information
            context_str = json.dumps(context or {}, indent=2, default=str)
            
            # Get user's emotional history for context
            if user_id and user_id in self.user_emotional_histories:
                recent_emotions = list(self.user_emotional_histories[user_id])[-5:]
                context_str += f"\nRecent emotional history: {[e.primary_emotion.value for e in recent_emotions]}"
            
            # Generate analysis using Gemini
            prompt = self.advanced_emotion_prompt.format(
                text=text,
                context=context_str
            )
            
            response = self.gemini_service.model.generate_content(prompt)
            
            # Parse JSON response
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                
                # Create emotional state with safe emotion mapping
                primary_emotion_str = result.get('primary_emotion', 'neutral').lower()
                try:
                    primary_emotion = EmotionType(primary_emotion_str)
                except ValueError:
                    # Map unknown emotions to closest known ones
                    emotion_mapping = {
                        'grief': EmotionType.SADNESS,
                        'happiness': EmotionType.JOY,
                        'worry': EmotionType.ANXIETY,
                        'rage': EmotionType.ANGER,
                        'terror': EmotionType.FEAR,
                        'melancholy': EmotionType.SADNESS,
                        'elation': EmotionType.JOY,
                        'panic': EmotionType.ANXIETY,
                        'fury': EmotionType.ANGER,
                        'dread': EmotionType.FEAR
                    }
                    primary_emotion = emotion_mapping.get(primary_emotion_str, EmotionType.NEUTRAL)

                # Safe secondary emotions mapping
                secondary_emotions = {}
                for k, v in result.get('secondary_emotions', {}).items():
                    try:
                        emotion_type = EmotionType(k.lower())
                        secondary_emotions[emotion_type] = v
                    except ValueError:
                        # Skip unknown emotions
                        continue

                emotional_state = EmotionalState(
                    primary_emotion=primary_emotion,
                    secondary_emotions=secondary_emotions,
                    intensity=float(result.get('intensity', 0.5)),
                    confidence=float(result.get('confidence', 0.5)),
                    valence=float(result.get('valence', 0.0)),
                    arousal=float(result.get('arousal', 0.0)),
                    stability=self._calculate_emotional_stability(user_id),
                    context_factors=result.get('context_factors', []),
                    timestamp=utc_now()
                )
                
                # Store in user's emotional history
                if user_id:
                    self.user_emotional_histories[user_id].append(emotional_state)
                
                return emotional_state
            
        except Exception as e:
            print(f"Error in advanced emotion analysis: {e}")
        
        # Fallback to basic emotional state
        return EmotionalState(
            primary_emotion=EmotionType.NEUTRAL,
            confidence=0.3,
            timestamp=utc_now()
        )
    
    def create_empathy_model(
        self, 
        user_id: str, 
        emotional_state: EmotionalState,
        conversation_history: List[Dict[str, Any]]
    ) -> EmpathyModel:
        """Create or update empathy model for user."""
        
        # Get emotional history
        emotional_history = list(self.user_emotional_histories.get(user_id, []))
        
        # Determine response strategy
        response_strategy = self._determine_response_strategy(emotional_state, emotional_history)
        
        # Calculate empathy level based on emotional intensity and user needs
        empathy_level = min(0.9, 0.5 + (emotional_state.intensity * 0.4))
        
        # Determine comfort techniques
        comfort_techniques = self._select_comfort_techniques(emotional_state)
        
        # Calculate emotional mirroring level
        emotional_mirroring = self._calculate_emotional_mirroring(emotional_state)
        
        empathy_model = EmpathyModel(
            user_emotional_state=emotional_state,
            emotional_history=emotional_history[-10:],  # Last 10 states
            empathy_level=empathy_level,
            response_strategy=response_strategy,
            emotional_mirroring=emotional_mirroring,
            comfort_techniques=comfort_techniques
        )
        
        # Store for user
        self.user_empathy_models[user_id] = empathy_model
        
        return empathy_model
    
    def generate_empathetic_response(
        self,
        empathy_model: EmpathyModel,
        conversation_history: List[Dict[str, Any]],
        user_message: str
    ) -> str:
        """Generate an empathetic response using the empathy model."""
        try:
            # Format conversation history
            history_text = ""
            for msg in conversation_history[-5:]:  # Last 5 messages
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                history_text += f"{role}: {content}\n"
            
            # Prepare emotional state summary
            emotional_state_summary = {
                'primary_emotion': empathy_model.user_emotional_state.primary_emotion.value,
                'intensity': empathy_model.user_emotional_state.intensity,
                'valence': empathy_model.user_emotional_state.valence,
                'support_needed': empathy_model.should_provide_support()
            }
            
            # Generate empathetic response
            prompt = self.empathy_response_prompt.format(
                emotional_state=json.dumps(emotional_state_summary, indent=2, default=str),
                conversation_history=history_text,
                empathy_strategy=empathy_model.response_strategy,
                support_techniques=", ".join(empathy_model.comfort_techniques)
            )
            
            response = self.gemini_service.model.generate_content(prompt)
            return response.text.strip()
            
        except Exception as e:
            # Fallback empathetic response
            if empathy_model.should_provide_support():
                return "I can sense you're going through something difficult right now. I'm here to listen and support you. Would you like to talk about what's on your mind?"
            else:
                return "I appreciate you sharing that with me. How are you feeling about everything?"
    
    def _calculate_emotional_stability(self, user_id: Optional[str]) -> float:
        """Calculate emotional stability based on recent emotional history."""
        if not user_id or user_id not in self.user_emotional_histories:
            return 0.5
        
        history = list(self.user_emotional_histories[user_id])
        if len(history) < 3:
            return 0.5
        
        # Calculate variance in emotional valence
        valences = [state.valence for state in history[-10:]]
        variance = np.var(valences) if len(valences) > 1 else 0
        
        # Stability is inverse of variance (normalized)
        stability = max(0.0, min(1.0, 1.0 - (variance * 2)))
        return stability
    
    def _determine_response_strategy(
        self,
        emotional_state: EmotionalState,
        emotional_history: List[EmotionalState]
    ) -> str:
        """Determine the best response strategy based on emotional context."""

        # Check if user needs support based on emotional state
        needs_support = self._needs_emotional_support(emotional_state)

        if needs_support:
            if emotional_state.primary_emotion in [EmotionType.SADNESS]:
                return "comforting"
            elif emotional_state.primary_emotion in [EmotionType.ANGER, EmotionType.FRUSTRATION]:
                return "validating"
            elif emotional_state.primary_emotion in [EmotionType.ANXIETY, EmotionType.FEAR]:
                return "reassuring"
            elif emotional_state.primary_emotion in [EmotionType.LONELINESS]:
                return "connecting"
            else:
                return "supportive"
        elif emotional_state.primary_emotion in [EmotionType.JOY, EmotionType.EXCITEMENT]:
            return "celebrating"
        elif emotional_state.primary_emotion in [EmotionType.HOPE, EmotionType.PRIDE]:
            return "encouraging"
        else:
            return "engaging"
    
    def _select_comfort_techniques(self, emotional_state: EmotionalState) -> List[str]:
        """Select appropriate comfort techniques based on emotional state."""
        techniques = []
        
        if emotional_state.primary_emotion == EmotionType.ANXIETY:
            techniques.extend(["breathing_exercises", "grounding_techniques", "reassurance"])
        elif emotional_state.primary_emotion == EmotionType.SADNESS:
            techniques.extend(["active_listening", "validation", "gentle_encouragement"])
        elif emotional_state.primary_emotion == EmotionType.ANGER:
            techniques.extend(["validation", "perspective_taking", "calming_presence"])
        elif emotional_state.primary_emotion == EmotionType.LONELINESS:
            techniques.extend(["connection", "shared_experiences", "companionship"])
        elif emotional_state.primary_emotion == EmotionType.STRESS:
            techniques.extend(["stress_relief", "problem_solving", "emotional_support"])
        
        # Add general techniques based on intensity
        if emotional_state.intensity > 0.7:
            techniques.append("crisis_support")
        elif emotional_state.intensity > 0.5:
            techniques.append("active_support")
        else:
            techniques.append("gentle_support")
        
        return techniques
    
    def _calculate_emotional_mirroring(self, emotional_state: EmotionalState) -> float:
        """Calculate appropriate level of emotional mirroring."""
        # Higher mirroring for positive emotions, moderate for negative
        if emotional_state.valence > 0:
            return min(0.7, 0.3 + (emotional_state.valence * 0.4))
        else:
            return max(0.1, 0.3 + (emotional_state.valence * 0.2))

    def _needs_emotional_support(self, emotional_state: EmotionalState) -> bool:
        """Determine if user needs emotional support based on emotional state."""
        negative_emotions = [
            EmotionType.SADNESS, EmotionType.ANGER, EmotionType.FEAR,
            EmotionType.ANXIETY, EmotionType.FRUSTRATION, EmotionType.LONELINESS,
            EmotionType.DISAPPOINTMENT, EmotionType.SHAME, EmotionType.STRESS,
            EmotionType.OVERWHELM
        ]
        return (
            emotional_state.primary_emotion in negative_emotions or
            emotional_state.valence < -0.3 or
            any(emotion in negative_emotions for emotion in emotional_state.get_dominant_emotions())
        )
    
    def get_emotional_insights(self, user_id: str) -> Dict[str, Any]:
        """Get emotional insights for a user."""
        if user_id not in self.user_emotional_histories:
            return {"message": "No emotional data available"}
        
        history = list(self.user_emotional_histories[user_id])
        
        # Calculate emotional patterns
        emotion_counts = defaultdict(int)
        total_valence = 0
        total_intensity = 0
        
        for state in history:
            emotion_counts[state.primary_emotion.value] += 1
            total_valence += state.valence
            total_intensity += state.intensity
        
        avg_valence = total_valence / len(history) if history else 0
        avg_intensity = total_intensity / len(history) if history else 0
        
        return {
            "total_interactions": len(history),
            "average_valence": round(avg_valence, 2),
            "average_intensity": round(avg_intensity, 2),
            "emotional_stability": round(self._calculate_emotional_stability(user_id), 2),
            "most_common_emotions": dict(sorted(emotion_counts.items(), key=lambda x: x[1], reverse=True)[:5]),
            "recent_trend": self._analyze_emotional_trend(history[-10:]) if len(history) >= 3 else "insufficient_data"
        }
    
    def _analyze_emotional_trend(self, recent_states: List[EmotionalState]) -> str:
        """Analyze recent emotional trend."""
        if len(recent_states) < 3:
            return "insufficient_data"
        
        valences = [state.valence for state in recent_states]
        
        # Simple trend analysis
        if all(v >= valences[i-1] for i, v in enumerate(valences[1:], 1)):
            return "improving"
        elif all(v <= valences[i-1] for i, v in enumerate(valences[1:], 1)):
            return "declining"
        elif valences[-1] > valences[0]:
            return "generally_positive"
        elif valences[-1] < valences[0]:
            return "generally_negative"
        else:
            return "stable"
