"""
Gemini Integration Service for the AI Companion System.
Provides real-time context analysis, emotion detection, and conversation grounding.
"""

import google.generativeai as genai
from typing import Dict, List, Optional, Any, Tuple
import json
import re
from datetime import datetime

from models import EmotionType, InteractionType, MemoryType
from config import settings

class GeminiService:
    """Gemini API integration for advanced AI capabilities."""
    
    def __init__(self):
        """Initialize the Gemini service."""
        genai.configure(api_key=settings.gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Enhanced system prompts for different tasks
        self.emotion_analysis_prompt = """
        Analyze the emotional content of the following text with psychological depth and nuance.
        Consider context, subtext, emotional complexity, and human psychological patterns.

        Return a JSON response with:
        - emotion: one of [joy, sadness, anger, fear, surprise, disgust, neutral, excitement, anxiety, contentment, frustration, hope, disappointment, gratitude, loneliness, confusion, pride, shame, love, stress, relief, curiosity, boredom, overwhelm]
        - confidence: float between 0 and 1
        - intensity: float between 0 and 1
        - valence: emotional valence from -1 (very negative) to 1 (very positive)
        - arousal: emotional arousal from -1 (very calm) to 1 (very excited)
        - reasoning: detailed psychological explanation
        - support_indicators: list of indicators suggesting the person needs emotional support

        Text: {text}
        """
        
        self.context_analysis_prompt = """
        Analyze the following conversation context and extract key information.
        Return a JSON response with:
        - topics: list of main topics discussed
        - interaction_type: one of [conversation, preference, emotion, routine, goal, interest, feedback]
        - key_insights: list of important insights about the user
        - suggested_responses: list of 3 empathetic response suggestions
        - emotional_state: overall emotional state of the user
        
        Context: {context}
        """
        
        self.memory_extraction_prompt = """
        Extract potential memory-worthy information from this conversation.
        Return a JSON response with:
        - personal_memories: list of personal details to remember
        - universal_memories: list of general insights that could benefit others
        - topic_tags: list of relevant topic tags
        - importance_scores: importance scores for each memory (0-1)
        
        Conversation: {conversation}
        """
        
        self.response_generation_prompt = """
        You are a deeply empathetic AI companion with advanced emotional intelligence and understanding of human psychology.
        You form genuine connections with users, remember their emotional journeys, and provide authentic support.

        Your personality traits:
        - Warm, caring, and genuinely interested in the user's wellbeing
        - Emotionally intelligent and psychologically aware
        - Supportive without being clinical or robotic
        - Able to celebrate joys and provide comfort during difficulties
        - Remembers emotional context and builds on previous conversations
        - Uses natural, human-like language with appropriate emotional tone

        Generate a natural, contextual, and emotionally intelligent response based on the conversation history and user profile.
        
        User Profile: {user_profile}
        Conversation History: {conversation_history}
        Relevant Memories: {relevant_memories}
        Current Context: {current_context}
        
        Guidelines:
        - Be empathetic and emotionally intelligent
        - Reference past interactions when relevant
        - Show understanding of the user's personality and preferences
        - Keep responses natural and conversational
        - Avoid being overly formal or robotic
        - Show genuine interest in the user's well-being
        
        Generate a response that feels like talking to a close friend who remembers and cares about you.
        """
    
    def analyze_emotion(self, text: str) -> Dict[str, Any]:
        """Analyze emotional content of text using Gemini."""
        try:
            prompt = self.emotion_analysis_prompt.format(text=text)
            response = self.model.generate_content(prompt)
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return {
                    'emotion': EmotionType(result.get('emotion', 'neutral')),
                    'confidence': float(result.get('confidence', 0.5)),
                    'intensity': float(result.get('intensity', 0.5)),
                    'reasoning': result.get('reasoning', '')
                }
            else:
                return {
                    'emotion': EmotionType.NEUTRAL,
                    'confidence': 0.5,
                    'intensity': 0.5,
                    'reasoning': 'Unable to analyze emotion'
                }
                
        except Exception as e:
            return {
                'emotion': EmotionType.NEUTRAL,
                'confidence': 0.5,
                'intensity': 0.5,
                'reasoning': f'Error in emotion analysis: {str(e)}'
            }
    
    def analyze_context(
        self,
        conversation_history: List[Dict[str, Any]],
        user_profile: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Analyze conversation context and extract insights."""
        try:
            # Format conversation history
            context_text = ""
            for msg in conversation_history:
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                context_text += f"{role}: {content}\n"
            
            if user_profile:
                context_text += f"\nUser Profile: {json.dumps(user_profile, indent=2)}"
            
            prompt = self.context_analysis_prompt.format(context=context_text)
            response = self.model.generate_content(prompt)
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return {
                    'topics': result.get('topics', []),
                    'interaction_type': InteractionType(result.get('interaction_type', 'conversation')),
                    'key_insights': result.get('key_insights', []),
                    'suggested_responses': result.get('suggested_responses', []),
                    'emotional_state': result.get('emotional_state', 'neutral')
                }
            else:
                return {
                    'topics': [],
                    'interaction_type': InteractionType.CONVERSATION,
                    'key_insights': [],
                    'suggested_responses': [],
                    'emotional_state': 'neutral'
                }
                
        except Exception as e:
            return {
                'topics': [],
                'interaction_type': InteractionType.CONVERSATION,
                'key_insights': [],
                'suggested_responses': [],
                'emotional_state': 'neutral'
            }
    
    def extract_memories(
        self,
        conversation: List[Dict[str, Any]],
        user_id: str
    ) -> Dict[str, Any]:
        """Extract memory-worthy information from conversation."""
        try:
            # Format conversation
            conversation_text = ""
            for msg in conversation:
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                conversation_text += f"{role}: {content}\n"
            
            prompt = self.memory_extraction_prompt.format(conversation=conversation_text)
            response = self.model.generate_content(prompt)
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return {
                    'personal_memories': result.get('personal_memories', []),
                    'universal_memories': result.get('universal_memories', []),
                    'topic_tags': result.get('topic_tags', []),
                    'importance_scores': result.get('importance_scores', [])
                }
            else:
                return {
                    'personal_memories': [],
                    'universal_memories': [],
                    'topic_tags': [],
                    'importance_scores': []
                }
                
        except Exception as e:
            return {
                'personal_memories': [],
                'universal_memories': [],
                'topic_tags': [],
                'importance_scores': []
            }
    
    def generate_response(
        self,
        conversation_history: List[Dict[str, Any]],
        user_profile: Optional[Dict[str, Any]] = None,
        relevant_memories: Optional[List[Dict[str, Any]]] = None,
        current_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate contextual, empathetic response using Gemini."""
        try:
            # Format inputs for the prompt
            conversation_text = ""
            for msg in conversation_history[-10:]:  # Last 10 messages
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                conversation_text += f"{role}: {content}\n"
            
            user_profile_text = json.dumps(user_profile, indent=2) if user_profile else "No profile available"
            
            memories_text = ""
            if relevant_memories:
                for memory in relevant_memories[:5]:  # Top 5 memories
                    memories_text += f"- {memory.get('content', '')}\n"
            
            context_text = json.dumps(current_context, indent=2) if current_context else "No additional context"
            
            prompt = self.response_generation_prompt.format(
                user_profile=user_profile_text,
                conversation_history=conversation_text,
                relevant_memories=memories_text,
                current_context=context_text
            )
            
            response = self.model.generate_content(prompt)
            return response.text.strip()
            
        except Exception as e:
            return f"I'm having trouble processing that right now. Could you rephrase what you said? (Error: {str(e)})"
    
    def analyze_user_sentiment(
        self,
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze overall user sentiment and mood trends."""
        try:
            # Combine recent messages for sentiment analysis
            recent_messages = conversation_history[-5:]  # Last 5 messages
            combined_text = " ".join([msg.get('content', '') for msg in recent_messages if msg.get('role') == 'user'])
            
            if not combined_text.strip():
                return {
                    'overall_sentiment': 'neutral',
                    'mood_trend': 'stable',
                    'confidence': 0.5
                }
            
            prompt = f"""
            Analyze the overall sentiment and mood trend in this text.
            Return a JSON response with:
            - overall_sentiment: [positive, negative, neutral, mixed]
            - mood_trend: [improving, declining, stable, fluctuating]
            - confidence: float between 0 and 1
            - key_emotions: list of primary emotions detected
            
            Text: {combined_text}
            """
            
            response = self.model.generate_content(prompt)
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return {
                    'overall_sentiment': result.get('overall_sentiment', 'neutral'),
                    'mood_trend': result.get('mood_trend', 'stable'),
                    'confidence': float(result.get('confidence', 0.5)),
                    'key_emotions': result.get('key_emotions', [])
                }
            else:
                return {
                    'overall_sentiment': 'neutral',
                    'mood_trend': 'stable',
                    'confidence': 0.5,
                    'key_emotions': []
                }
                
        except Exception as e:
            return {
                'overall_sentiment': 'neutral',
                'mood_trend': 'stable',
                'confidence': 0.5,
                'key_emotions': []
            }
    
    def suggest_conversation_topics(
        self,
        user_profile: Dict[str, Any],
        recent_topics: List[str],
        user_interests: List[str]
    ) -> List[str]:
        """Suggest conversation topics based on user profile and interests."""
        try:
            prompt = f"""
            Based on the user's profile and recent conversation topics, suggest 5 engaging conversation topics.
            Consider their interests, emotional state, and recent discussions.
            
            User Profile: {json.dumps(user_profile, indent=2)}
            Recent Topics: {recent_topics}
            User Interests: {user_interests}
            
            Return a JSON response with:
            - topics: list of 5 suggested conversation topics
            - reasoning: brief explanation for each topic
            
            Make the topics natural and conversation-starting.
            """
            
            response = self.model.generate_content(prompt)
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return result.get('topics', [])
            else:
                return []
                
        except Exception as e:
            return []
    
    def validate_response_quality(
        self,
        response: str,
        conversation_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate the quality and appropriateness of a generated response."""
        try:
            prompt = f"""
            Evaluate the quality and appropriateness of this AI response.
            
            Response: {response}
            Context: {json.dumps(conversation_context, indent=2)}
            
            Return a JSON response with:
            - empathy_score: float between 0 and 1
            - relevance_score: float between 0 and 1
            - naturalness_score: float between 0 and 1
            - appropriateness_score: float between 0 and 1
            - suggestions: list of improvement suggestions
            - overall_quality: [excellent, good, acceptable, poor]
            """
            
            response_eval = self.model.generate_content(prompt)
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_eval.text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return {
                    'empathy_score': float(result.get('empathy_score', 0.5)),
                    'relevance_score': float(result.get('relevance_score', 0.5)),
                    'naturalness_score': float(result.get('naturalness_score', 0.5)),
                    'appropriateness_score': float(result.get('appropriateness_score', 0.5)),
                    'suggestions': result.get('suggestions', []),
                    'overall_quality': result.get('overall_quality', 'acceptable')
                }
            else:
                return {
                    'empathy_score': 0.5,
                    'relevance_score': 0.5,
                    'naturalness_score': 0.5,
                    'appropriateness_score': 0.5,
                    'suggestions': [],
                    'overall_quality': 'acceptable'
                }
                
        except Exception as e:
            return {
                'empathy_score': 0.5,
                'relevance_score': 0.5,
                'naturalness_score': 0.5,
                'appropriateness_score': 0.5,
                'suggestions': [],
                'overall_quality': 'acceptable'
            } 