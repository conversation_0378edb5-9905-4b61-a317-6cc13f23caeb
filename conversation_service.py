"""
Conversation Service for the AI Companion System.
Orchestrates memory, learning, and AI services for intelligent conversations.
"""

import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

# Configure logging
logger = logging.getLogger(__name__)

from models import (
    UserProfile, EmotionType, InteractionType, MemoryType, ContextualMemory
)
from memory_service import MemoryService
from learning_service import LearningService
from gemini_service import GeminiService
from storage_service import PersistentStorageService
from config import settings

@dataclass
class ConversationContext:
    """Context for ongoing conversation."""
    user_id: str
    conversation_id: str
    user_profile: Optional[UserProfile]
    conversation_history: List[Dict[str, Any]]
    contextual_memory: Optional[ContextualMemory]
    emotional_state: Dict[str, Any]
    current_topics: List[str]

class ConversationService:
    """Main conversation service orchestrating all AI companion capabilities."""
    
    def __init__(self):
        """Initialize the conversation service."""
        self.memory_service = MemoryService()
        self.gemini_service = GeminiService()
        self.learning_service = LearningService(self.memory_service, self.gemini_service)
        self.storage_service = PersistentStorageService()

        # Active conversations (in-memory for performance)
        self.active_conversations: Dict[str, ConversationContext] = {}

        # Load active conversations from storage on startup
        self._load_active_conversations()
        
    def start_conversation(self, user_id: str, user_name: Optional[str] = None) -> str:
        """Start a new conversation session."""
        conversation_id = str(uuid.uuid4())
        
        # Get or create user profile
        user_profile = self.memory_service.get_user_profile(user_id)
        if not user_profile:
            user_profile = self.memory_service.create_user_profile(user_id, user_name)
        
        # Create conversation context
        context = ConversationContext(
            user_id=user_id,
            conversation_id=conversation_id,
            user_profile=user_profile,
            conversation_history=[],
            contextual_memory=None,
            emotional_state={'current_emotion': EmotionType.NEUTRAL, 'confidence': 0.5},
            current_topics=[]
        )
        
        self.active_conversations[conversation_id] = context
        
        return conversation_id
    
    def process_message(
        self,
        conversation_id: str,
        user_message: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process a user message and generate a contextual response."""
        try:
            # Get conversation context
            context = self.active_conversations.get(conversation_id)
            if not context:
                if user_id:
                    conversation_id = self.start_conversation(user_id)
                    context = self.active_conversations[conversation_id]
                else:
                    raise ValueError("Invalid conversation ID and no user ID provided")
            
            # Add user message to history
            user_msg = {
                'role': 'user',
                'content': user_message,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            context.conversation_history.append(user_msg)
            
            # Analyze emotion in user message
            emotion_analysis = self.gemini_service.analyze_emotion(user_message)
            user_msg['emotion'] = emotion_analysis['emotion']
            user_msg['emotion_confidence'] = emotion_analysis['confidence']
            
            # Update emotional state
            context.emotional_state = {
                'current_emotion': emotion_analysis['emotion'],
                'confidence': emotion_analysis['confidence'],
                'intensity': emotion_analysis['intensity']
            }
            
            # Analyze conversation context
            context_analysis = self.gemini_service.analyze_context(
                context.conversation_history,
                context.user_profile.dict() if context.user_profile else None
            )
            
            # Update current topics
            context.current_topics = context_analysis.get('topics', [])
            
            # Create contextual memory
            context.contextual_memory = self.memory_service.create_contextual_memory(
                conversation_id, context.user_id, user_message
            )
            
            # Extract and store memories
            self._extract_and_store_memories(context, user_message, emotion_analysis)
            
            # Generate response
            response = self._generate_contextual_response(context)
            
            # Add assistant response to history
            assistant_msg = {
                'role': 'assistant',
                'content': response,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            context.conversation_history.append(assistant_msg)
            
            # Update user profile
            self._update_user_profile(context)
            
            # Generate learning insights
            insights = self.learning_service.generate_learning_insights(
                context.user_id, context.conversation_history[-10:]  # Last 10 interactions
            )
            
            # Curate personalized content
            personalized_content = self.learning_service.curate_personalized_content(
                context.user_id, "conversation_starters"
            )

            # Periodically persist conversation state
            self._persist_conversation_state(conversation_id)

            return {
                'conversation_id': conversation_id,
                'response': response,
                'emotion_analysis': emotion_analysis,
                'context_analysis': context_analysis,
                'personalized_content': personalized_content,
                'insights_generated': len(insights),
                'user_profile_updated': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'response': "I'm having trouble processing that right now. Could you try again?"
            }
    
    def get_conversation_history(
        self,
        conversation_id: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get conversation history."""
        context = self.active_conversations.get(conversation_id)
        if not context:
            return []
        
        return context.conversation_history[-limit:]
    
    def get_user_insights(self, user_id: str) -> Dict[str, Any]:
        """Get insights about a user."""
        try:
            # Get user profile
            user_profile = self.memory_service.get_user_profile(user_id)
            if not user_profile:
                return {'error': 'User not found'}
            
            # Get memory statistics
            memory_stats = self.memory_service.get_memory_statistics(user_id)
            
            # Get recent memories
            recent_memories = self.memory_service.retrieve_relevant_memories(
                user_id, "", MemoryType.PERSONAL, limit=10
            )
            
            # Analyze user patterns
            user_patterns = self.learning_service.analyze_user_preferences(
                user_id, []  # Would need conversation history here
            )
            
            # Predict user needs
            predicted_needs = self.learning_service.predict_user_needs(
                user_id, {'current_time': datetime.now(timezone.utc).isoformat()}
            )
            
            return {
                'user_profile': user_profile.dict(),
                'memory_statistics': memory_stats,
                'recent_memories': [memory.dict() for memory in recent_memories],
                'user_patterns': user_patterns,
                'predicted_needs': predicted_needs
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def end_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """End a conversation session."""
        context = self.active_conversations.get(conversation_id)
        if not context:
            return {'error': 'Conversation not found'}
        
        try:
            # Generate conversation summary
            summary = self._generate_conversation_summary(context)
            
            # Store final memories
            self._store_conversation_memories(context)

            # Persist conversation to storage
            emotional_arc = self._analyze_emotional_arc(context)
            self.storage_service.store_conversation(
                conversation_id=conversation_id,
                user_id=context.user_id,
                messages=context.conversation_history,
                summary=summary,
                topics=context.current_topics,
                emotions=[emotion.value for emotion in emotional_arc.get('emotions', [])]
            )

            # Clean up
            del self.active_conversations[conversation_id]

            return {
                'conversation_id': conversation_id,
                'summary': summary,
                'total_messages': len(context.conversation_history),
                'topics_discussed': context.current_topics,
                'emotional_arc': emotional_arc
            }
            
        except Exception as e:
            return {'error': str(e)}

    def get_conversation_history(self, conversation_id: str) -> List[Dict[str, Any]]:
        """Get conversation history from active memory or storage."""
        # Try active conversations first
        context = self.active_conversations.get(conversation_id)
        if context:
            return context.conversation_history

        # Fallback to persistent storage
        stored_conversation = self.storage_service.retrieve_conversation(conversation_id)
        if stored_conversation:
            return stored_conversation['messages']

        return []

    def _load_active_conversations(self):
        """Load active conversations from storage on startup."""
        # This would load conversations marked as active from storage
        # For now, we start with empty active conversations
        pass

    def _persist_conversation_state(self, conversation_id: str):
        """Periodically persist conversation state during long conversations."""
        context = self.active_conversations.get(conversation_id)
        if context and len(context.conversation_history) % 10 == 0:  # Every 10 messages
            # Store intermediate state
            self.storage_service.store_conversation(
                conversation_id=conversation_id,
                user_id=context.user_id,
                messages=context.conversation_history,
                topics=context.current_topics
            )
    
    def _extract_and_store_memories(
        self,
        context: ConversationContext,
        user_message: str,
        emotion_analysis: Dict[str, Any]
    ):
        """Extract and store memories from the conversation."""
        try:
            # Extract memories using Gemini
            memory_extraction = self.gemini_service.extract_memories(
                context.conversation_history, context.user_id
            )
            
            # Store personal memories
            for i, memory_content in enumerate(memory_extraction.get('personal_memories', [])):
                importance = memory_extraction.get('importance_scores', [0.5])[i] if memory_extraction.get('importance_scores') else 0.5
                
                self.memory_service.store_personal_memory(
                    user_id=context.user_id,
                    content=memory_content,
                    interaction_type=InteractionType.CONVERSATION,
                    emotion=emotion_analysis['emotion'],
                    importance=importance
                )
            
            # Store universal memories
            for i, memory_content in enumerate(memory_extraction.get('universal_memories', [])):
                topic_tags = memory_extraction.get('topic_tags', [])
                
                self.memory_service.store_universal_memory(
                    content=memory_content,
                    interaction_type=InteractionType.CONVERSATION,
                    topic_tags=topic_tags,
                    emotion=emotion_analysis['emotion'],
                    source_user_id=context.user_id
                )
                
        except Exception as e:
            logger.error(f"Failed to extract memories for user {context.user_id}: {e}")
            # Fallback: store basic memory
            self.memory_service.store_personal_memory(
                user_id=context.user_id,
                content=user_message,
                interaction_type=InteractionType.CONVERSATION,
                emotion=emotion_analysis['emotion']
            )
    
    def _generate_contextual_response(self, context: ConversationContext) -> str:
        """Generate a contextual, empathetic response."""
        try:
            # Get relevant memories
            relevant_memories = []
            if context.contextual_memory:
                relevant_memories = [memory.dict() for memory in context.contextual_memory.relevant_memories]
            
            # Generate base response using Gemini
            response = self.gemini_service.generate_response(
                conversation_history=context.conversation_history,
                user_profile=context.user_profile.dict() if context.user_profile else None,
                relevant_memories=relevant_memories,
                current_context={
                    'emotional_state': context.emotional_state,
                    'current_topics': context.current_topics,
                    'conversation_id': context.conversation_id
                }
            )
            
            # Adapt response style if user profile exists
            if context.user_profile:
                response = self.learning_service.adapt_conversation_style(
                    context.user_id, response, context.user_profile
                )
            
            return response
            
        except Exception as e:
            return f"I'm having trouble generating a response right now. Could you try again? (Error: {str(e)})"
    
    def _update_user_profile(self, context: ConversationContext):
        """Update user profile based on conversation."""
        if not context.user_profile:
            return
        
        try:
            # Update interaction count
            context.user_profile.interaction_count += 1
            context.user_profile.last_interaction = datetime.now(timezone.utc)
            
            # Update emotional patterns
            current_emotion = context.emotional_state.get('current_emotion', EmotionType.NEUTRAL)
            if current_emotion:
                current_weight = context.user_profile.emotional_patterns.get(current_emotion, 0.0)
                context.user_profile.emotional_patterns[current_emotion] = current_weight + settings.learning_rate
            
            # Normalize emotional patterns
            total_weight = sum(context.user_profile.emotional_patterns.values())
            if total_weight > 0:
                for emotion_type in context.user_profile.emotional_patterns:
                    context.user_profile.emotional_patterns[emotion_type] /= total_weight
            
            # Update interests based on current topics
            for topic in context.current_topics:
                if topic not in context.user_profile.interests:
                    context.user_profile.interests.append(topic)
            
            # Keep only top 20 interests
            context.user_profile.interests = context.user_profile.interests[-20:]
            
        except Exception as e:
            logger.error(f"Failed to update user profile for {context.user_id}: {e}")
            # Don't fail the conversation if profile update fails
    
    def _generate_conversation_summary(self, context: ConversationContext) -> str:
        """Generate a summary of the conversation."""
        try:
            # Use Gemini to generate summary
            conversation_text = ""
            for msg in context.conversation_history:
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                conversation_text += f"{role}: {content}\n"
            
            summary_prompt = f"""
            Generate a brief summary of this conversation, highlighting:
            - Main topics discussed
            - Key insights about the user
            - Emotional journey
            - Any important details to remember
            
            Conversation:
            {conversation_text}
            
            Summary:
            """
            
            response = self.gemini_service.model.generate_content(summary_prompt)
            return response.text.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate conversation summary: {e}")
            return f"Conversation with {len(context.conversation_history)} messages covering topics: {', '.join(context.current_topics)}"
    
    def _store_conversation_memories(self, context: ConversationContext):
        """Store final conversation memories."""
        try:
            # Store conversation summary as memory
            summary = self._generate_conversation_summary(context)
            
            self.memory_service.store_personal_memory(
                user_id=context.user_id,
                content=f"Conversation summary: {summary}",
                interaction_type=InteractionType.CONVERSATION,
                context={
                    'conversation_id': context.conversation_id,
                    'message_count': len(context.conversation_history),
                    'topics': context.current_topics
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to store conversation memories: {e}")
            # Don't fail if memory storage fails
    
    def _analyze_emotional_arc(self, context: ConversationContext) -> Dict[str, Any]:
        """Analyze the emotional arc of the conversation."""
        emotions = []
        for msg in context.conversation_history:
            if msg.get('role') == 'user' and msg.get('emotion'):
                emotions.append({
                    'emotion': msg['emotion'],
                    'timestamp': msg['timestamp'],
                    'confidence': msg.get('emotion_confidence', 0.5)
                })
        
        if not emotions:
            return {'emotional_arc': 'stable', 'emotion_changes': 0}
        
        # Count emotion changes
        emotion_changes = 0
        for i in range(1, len(emotions)):
            if emotions[i]['emotion'] != emotions[i-1]['emotion']:
                emotion_changes += 1
        
        # Determine arc type
        if emotion_changes == 0:
            arc_type = 'stable'
        elif emotion_changes <= 2:
            arc_type = 'gradual'
        else:
            arc_type = 'dynamic'
        
        return {
            'emotional_arc': arc_type,
            'emotion_changes': emotion_changes,
            'emotions_tracked': len(emotions),
            'final_emotion': emotions[-1]['emotion'] if emotions else 'neutral'
        } 